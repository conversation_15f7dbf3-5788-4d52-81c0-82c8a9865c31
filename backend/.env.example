# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/maschain_energy

# MasChain L1 Configuration
MASCHAIN_ENVIRONMENT=testnet
MASCHAIN_API_URL=https://service-testnet.maschain.com
MASCHAIN_API_KEY=your_api_key_here
MASCHAIN_API_SECRET=your_api_secret_here
MASCHAIN_PROJECT_ID=your_project_id_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here_change_in_production
VALIDATOR_JWT_SECRET=your_validator_jwt_secret_here

# Energy Credit Token
ENERGY_CREDIT_TOKEN_ID=your_energy_token_id_here

# Escrow Configuration
ESCROW_WALLET_ADDRESS=your_escrow_wallet_address_here

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# Oracle Configuration
ORACLE_VALIDATION_THRESHOLD=2
ORACLE_MAX_READING_AGE_HOURS=24

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# Development/Testing
ENABLE_MOCK_DATA=true
MOCK_BLOCKCHAIN_RESPONSES=false
