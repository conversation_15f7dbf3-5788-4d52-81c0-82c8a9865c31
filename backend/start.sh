#!/bin/bash

# MasChain Energy Trading Platform - Backend Startup Script

echo "🚀 Starting MasChain Energy Trading Platform Backend"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

# Check if we're in the backend directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the backend directory"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please update it with your MasChain credentials."
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed"
fi

# Check if TypeScript is compiled
if [ ! -d "dist" ]; then
    echo "🔨 Compiling TypeScript..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ TypeScript compilation failed"
        exit 1
    fi
    echo "✅ TypeScript compiled"
fi

# Check if PostgreSQL is running (optional)
if command -v pg_isready &> /dev/null; then
    if ! pg_isready -q; then
        echo "⚠️  PostgreSQL is not running. Database features may not work."
        echo "   Start PostgreSQL or use Docker: docker-compose up -d postgres"
    else
        echo "✅ PostgreSQL is running"
    fi
fi

# Start the server
echo ""
echo "🚀 Starting the backend server..."
echo "   Environment: ${NODE_ENV:-development}"
echo "   Port: ${PORT:-3001}"
echo ""

# Use development mode with hot reload if in development
if [ "${NODE_ENV}" = "production" ]; then
    echo "🏭 Starting in production mode..."
    npm start
else
    echo "🛠️  Starting in development mode with hot reload..."
    npm run dev
fi
