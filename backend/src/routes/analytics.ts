import express from 'express'
import { query, body, validationResult } from 'express-validator'
import { AnalyticsService } from '../services/analyticsService'
import { TransactionHistoryService } from '../services/transactionHistoryService'
import { authenticateWallet } from '../middleware/auth'
import { rateLimiter } from '../middleware/rateLimiter'

const router = express.Router()
const analyticsService = new AnalyticsService()
const transactionHistoryService = new TransactionHistoryService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    })
  }
  next()
}

/**
 * GET /api/analytics/insights
 * Get market insights
 */
router.get('/insights',
  async (req, res) => {
    try {
      const insights = await analyticsService.generateMarketInsights()
      res.json({ success: true, data: insights })
    } catch (error) {
      console.error('Error fetching market insights:', error)
      res.status(500).json({ error: 'Failed to fetch market insights' })
    }
  }
)

/**
 * GET /api/analytics/user-insights
 * Get personalized insights for authenticated user
 */
router.get('/user-insights',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const insights = await analyticsService.getUserInsights(walletAddress)
      res.json({ success: true, data: insights })
    } catch (error) {
      console.error('Error fetching user insights:', error)
      res.status(500).json({ error: 'Failed to fetch user insights' })
    }
  }
)

/**
 * GET /api/analytics/forecast
 * Generate energy forecast
 */
router.get('/forecast',
  authenticateWallet,
  [
    query('type').isIn(['production', 'consumption', 'price', 'demand']).withMessage('Invalid forecast type'),
    query('period').isIn(['1h', '24h', '7d', '30d']).withMessage('Invalid period'),
    query('deviceId').optional().isString().withMessage('Device ID must be a string')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      const { type, period, deviceId } = req.query

      const forecast = await analyticsService.generateForecast(
        type as 'production' | 'consumption' | 'price' | 'demand',
        period as '1h' | '24h' | '7d' | '30d',
        deviceId as string,
        walletAddress
      )

      res.json({ success: true, data: forecast })
    } catch (error) {
      console.error('Error generating forecast:', error)
      res.status(500).json({ error: 'Failed to generate forecast' })
    }
  }
)

/**
 * GET /api/analytics/carbon-footprint
 * Get carbon footprint analysis
 */
router.get('/carbon-footprint',
  authenticateWallet,
  [
    query('period').optional().isIn(['24h', '7d', '30d', '1y']).withMessage('Invalid period')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { period = '30d' } = req.query

      const carbonFootprint = await analyticsService.calculateCarbonFootprint(
        walletAddress,
        period as '24h' | '7d' | '30d' | '1y'
      )

      res.json({ success: true, data: carbonFootprint })
    } catch (error) {
      console.error('Error calculating carbon footprint:', error)
      res.status(500).json({ error: 'Failed to calculate carbon footprint' })
    }
  }
)

/**
 * GET /api/analytics/performance
 * Get performance analytics
 */
router.get('/performance',
  authenticateWallet,
  [
    query('period').optional().isIn(['24h', '7d', '30d', '1y']).withMessage('Invalid period')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { period = '30d' } = req.query

      const performance = await analyticsService.analyzePerformance(
        walletAddress,
        period as '24h' | '7d' | '30d' | '1y'
      )

      res.json({ success: true, data: performance })
    } catch (error) {
      console.error('Error analyzing performance:', error)
      res.status(500).json({ error: 'Failed to analyze performance' })
    }
  }
)

/**
 * GET /api/analytics/trends
 * Get market trends analysis
 */
router.get('/trends',
  async (req, res) => {
    try {
      const trends = await analyticsService.analyzeMarketTrends()
      res.json({ success: true, data: trends })
    } catch (error) {
      console.error('Error analyzing market trends:', error)
      res.status(500).json({ error: 'Failed to analyze market trends' })
    }
  }
)

/**
 * POST /api/analytics/alert-rules
 * Create a new alert rule
 */
router.post('/alert-rules',
  authenticateWallet,
  rateLimiter,
  [
    body('ruleType').isIn(['price_threshold', 'volume_threshold', 'efficiency_drop', 'device_offline', 'carbon_target']).withMessage('Invalid rule type'),
    body('conditions').isObject().withMessage('Conditions must be an object'),
    body('conditions.metric').isString().notEmpty().withMessage('Metric is required'),
    body('conditions.operator').isIn(['>', '<', '=', '>=', '<=']).withMessage('Invalid operator'),
    body('conditions.value').isNumeric().withMessage('Value must be numeric'),
    body('conditions.duration').optional().isInt({ min: 1 }).withMessage('Duration must be positive integer')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { ruleType, conditions } = req.body

      const alertRule = await analyticsService.createAlertRule({
        userId: walletAddress,
        ruleType,
        conditions,
        isActive: true
      })

      res.status(201).json({ success: true, data: alertRule })
    } catch (error) {
      console.error('Error creating alert rule:', error)
      res.status(500).json({ error: 'Failed to create alert rule' })
    }
  }
)

/**
 * GET /api/analytics/transaction-history
 * Get transaction history with analytics
 */
router.get('/transaction-history',
  authenticateWallet,
  [
    query('transactionType').optional().isString().withMessage('Transaction type must be a string'),
    query('status').optional().isString().withMessage('Status must be a string'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
    query('sortBy').optional().isIn(['timestamp', 'value', 'gasUsed']).withMessage('Invalid sort field'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Invalid sort order')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const {
        transactionType,
        status,
        startDate,
        endDate,
        limit = 50,
        offset = 0,
        sortBy = 'timestamp',
        sortOrder = 'desc'
      } = req.query

      const query = {
        userId: walletAddress,
        transactionType: transactionType as string,
        status: status as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        sortBy: sortBy as 'timestamp' | 'value' | 'gasUsed',
        sortOrder: sortOrder as 'asc' | 'desc'
      }

      const result = await transactionHistoryService.getTransactionHistory(query)
      res.json({ success: true, data: result })
    } catch (error) {
      console.error('Error fetching transaction history:', error)
      res.status(500).json({ error: 'Failed to fetch transaction history' })
    }
  }
)

/**
 * GET /api/analytics/trade-history
 * Get trade history with analytics
 */
router.get('/trade-history',
  authenticateWallet,
  [
    query('role').optional().isIn(['buyer', 'seller', 'both']).withMessage('Invalid role'),
    query('status').optional().isString().withMessage('Status must be a string'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const {
        role = 'both',
        status,
        startDate,
        endDate,
        limit = 50,
        offset = 0
      } = req.query

      const options = {
        role: role as 'buyer' | 'seller' | 'both',
        status: status as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      }

      const result = await transactionHistoryService.getTradeHistory(walletAddress, options)
      res.json({ success: true, data: result })
    } catch (error) {
      console.error('Error fetching trade history:', error)
      res.status(500).json({ error: 'Failed to fetch trade history' })
    }
  }
)

/**
 * GET /api/analytics/user-activity
 * Get user activity history
 */
router.get('/user-activity',
  authenticateWallet,
  [
    query('activityType').optional().isString().withMessage('Activity type must be a string'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const {
        activityType,
        startDate,
        endDate,
        limit = 50,
        offset = 0
      } = req.query

      const options = {
        activityType: activityType as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      }

      const result = await transactionHistoryService.getUserActivity(walletAddress, options)
      res.json({ success: true, data: result })
    } catch (error) {
      console.error('Error fetching user activity:', error)
      res.status(500).json({ error: 'Failed to fetch user activity' })
    }
  }
)

/**
 * GET /api/analytics/performance-metrics
 * Get performance metrics for a specific period
 */
router.get('/performance-metrics',
  authenticateWallet,
  [
    query('period').isIn(['24h', '7d', '30d', '1y']).withMessage('Invalid period')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { period } = req.query

      const metrics = await transactionHistoryService.getPerformanceMetrics(
        walletAddress,
        period as '24h' | '7d' | '30d' | '1y'
      )

      res.json({ success: true, data: metrics })
    } catch (error) {
      console.error('Error fetching performance metrics:', error)
      res.status(500).json({ error: 'Failed to fetch performance metrics' })
    }
  }
)

/**
 * GET /api/analytics/export-data
 * Export all user data (GDPR compliance)
 */
router.get('/export-data',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const userData = await transactionHistoryService.exportUserData(walletAddress)
      
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', `attachment; filename="energy-trading-data-${walletAddress}-${Date.now()}.json"`)
      res.json({ success: true, data: userData })
    } catch (error) {
      console.error('Error exporting user data:', error)
      res.status(500).json({ error: 'Failed to export user data' })
    }
  }
)

export default router
