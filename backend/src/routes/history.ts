import express from 'express'
import { body, query, validationResult } from 'express-validator'
import { authenticateUser } from '../middleware/auth'
import { TransactionHistoryService } from '../services/transactionHistoryService'

const router = express.Router()
const transactionHistoryService = new TransactionHistoryService()

/**
 * Validation middleware
 */
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ error: 'Validation failed', details: errors.array() })
  }
  next()
}

/**
 * GET /api/history/transactions
 * Get user's transaction history with analytics
 */
router.get('/transactions',
  authenticateUser,
  [
    query('type').optional().isIn(['trade', 'offer', 'energy_reading', 'all']).withMessage('Invalid transaction type'),
    query('status').optional().isString().withMessage('Invalid status'),
    query('period').optional().isIn(['7d', '30d', '90d', '1y']).withMessage('Invalid period'),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { 
        type = 'all', 
        status = 'all', 
        period = '30d',
        page = 1,
        limit = 20
      } = req.query

      // Calculate date range
      const endDate = new Date()
      const startDate = new Date()
      
      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      const query = {
        walletAddress,
        type: type === 'all' ? undefined : type as string,
        status: status === 'all' ? undefined : status as string,
        startDate,
        endDate,
        limit: parseInt(limit as string),
        offset: (parseInt(page as string) - 1) * parseInt(limit as string)
      }

      const result = await transactionHistoryService.getTransactionHistory(query)

      res.json({
        success: true,
        data: {
          transactions: result.transactions,
          total: result.total,
          summary: result.summary,
          analytics: result.analytics,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            totalPages: Math.ceil(result.total / parseInt(limit as string))
          }
        }
      })
    } catch (error) {
      console.error('Error fetching transaction history:', error)
      res.status(500).json({ error: 'Failed to fetch transaction history' })
    }
  }
)

/**
 * GET /api/history/trades
 * Get detailed trade history
 */
router.get('/trades',
  authenticateUser,
  [
    query('role').optional().isIn(['buyer', 'seller', 'both']).withMessage('Invalid role'),
    query('status').optional().isString().withMessage('Invalid status'),
    query('startDate').optional().isISO8601().withMessage('Invalid start date'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const options = {
        role: req.query.role as 'buyer' | 'seller' | 'both' | undefined,
        status: req.query.status as string | undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
        offset: req.query.offset ? parseInt(req.query.offset as string) : 0
      }

      const result = await transactionHistoryService.getTradeHistory(walletAddress, options)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      console.error('Error fetching trade history:', error)
      res.status(500).json({ error: 'Failed to fetch trade history' })
    }
  }
)

/**
 * GET /api/history/offers
 * Get offer history
 */
router.get('/offers',
  authenticateUser,
  [
    query('status').optional().isString().withMessage('Invalid status'),
    query('startDate').optional().isISO8601().withMessage('Invalid start date'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const options = {
        status: req.query.status as string | undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
        offset: req.query.offset ? parseInt(req.query.offset as string) : 0
      }

      const result = await transactionHistoryService.getOfferHistory(walletAddress, options)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      console.error('Error fetching offer history:', error)
      res.status(500).json({ error: 'Failed to fetch offer history' })
    }
  }
)

/**
 * GET /api/history/energy
 * Get energy production/consumption history
 */
router.get('/energy',
  authenticateUser,
  [
    query('type').optional().isIn(['production', 'consumption', 'both']).withMessage('Invalid energy type'),
    query('startDate').optional().isISO8601().withMessage('Invalid start date'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const options = {
        type: req.query.type as 'production' | 'consumption' | 'both' | undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
        offset: req.query.offset ? parseInt(req.query.offset as string) : 0
      }

      const result = await transactionHistoryService.getEnergyHistory(walletAddress, options)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      console.error('Error fetching energy history:', error)
      res.status(500).json({ error: 'Failed to fetch energy history' })
    }
  }
)

/**
 * GET /api/history/performance
 * Get performance metrics
 */
router.get('/performance',
  authenticateUser,
  [
    query('period').optional().isIn(['24h', '7d', '30d', '1y']).withMessage('Invalid period')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const period = (req.query.period as '24h' | '7d' | '30d' | '1y') || '30d'
      const metrics = await transactionHistoryService.getPerformanceMetrics(walletAddress, period)

      res.json({
        success: true,
        data: metrics
      })
    } catch (error) {
      console.error('Error fetching performance metrics:', error)
      res.status(500).json({ error: 'Failed to fetch performance metrics' })
    }
  }
)

/**
 * GET /api/history/export
 * Export user data (CSV format)
 */
router.get('/export',
  authenticateUser,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const userData = await transactionHistoryService.exportUserData(walletAddress)

      // Convert to CSV format
      const csvData = convertToCSV(userData)

      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="energy-trading-data-${new Date().toISOString().split('T')[0]}.csv"`)
      res.send(csvData)
    } catch (error) {
      console.error('Error exporting user data:', error)
      res.status(500).json({ error: 'Failed to export user data' })
    }
  }
)

/**
 * GET /api/history/activity
 * Get user activity log
 */
router.get('/activity',
  authenticateUser,
  [
    query('activityType').optional().isString().withMessage('Invalid activity type'),
    query('startDate').optional().isISO8601().withMessage('Invalid start date'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const options = {
        activityType: req.query.activityType as string | undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
        offset: req.query.offset ? parseInt(req.query.offset as string) : 0
      }

      const result = await transactionHistoryService.getUserActivity(walletAddress, options)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      console.error('Error fetching user activity:', error)
      res.status(500).json({ error: 'Failed to fetch user activity' })
    }
  }
)

/**
 * Helper function to convert data to CSV format
 */
function convertToCSV(data: any): string {
  const headers = ['Type', 'Date', 'Amount', 'Price', 'Total Value', 'Status', 'Counterparty', 'Transaction Hash']
  const rows = []

  // Add header row
  rows.push(headers.join(','))

  // Add transaction rows
  for (const transaction of data.transactions) {
    const row = [
      transaction.transactionType,
      transaction.timestamp,
      transaction.value || '',
      '', // Price would need to be calculated
      transaction.value || '',
      transaction.status,
      transaction.to || transaction.from,
      transaction.transactionHash
    ]
    rows.push(row.map(field => `"${field}"`).join(','))
  }

  // Add trade rows
  for (const trade of data.trades) {
    const row = [
      'trade',
      trade.executedAt,
      trade.energyAmount,
      trade.pricePerKwh,
      trade.totalPrice,
      trade.status,
      trade.buyer === req.user?.walletAddress ? trade.seller : trade.buyer,
      trade.transactionHash
    ]
    rows.push(row.map(field => `"${field}"`).join(','))
  }

  return rows.join('\n')
}

export default router
