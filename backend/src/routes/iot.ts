import express from 'express'
import { body, query, validationResult } from 'express-validator'
import { IoTService } from '../services/iotService'
import { authenticateWallet } from '../middleware/auth'
import { rateLimiter, oracleRateLimiter } from '../middleware/rateLimiter'

const router = express.Router()
const iotService = new IoTService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    })
  }
  next()
}

/**
 * POST /api/iot/devices
 * Register a new IoT device
 */
router.post('/devices',
  authenticateWallet,
  rateLimiter,
  [
    body('deviceId').isString().notEmpty().withMessage('Device ID is required'),
    body('deviceType').isIn(['smart_meter', 'solar_panel', 'battery', 'ev_charger']).withMessage('Invalid device type'),
    body('location').isObject().withMessage('Location must be an object'),
    body('location.latitude').isFloat({ min: -90, max: 90 }).withMessage('Invalid latitude'),
    body('location.longitude').isFloat({ min: -180, max: 180 }).withMessage('Invalid longitude'),
    body('location.address').isString().notEmpty().withMessage('Address is required'),
    body('specifications').isObject().withMessage('Specifications must be an object'),
    body('specifications.maxCapacity').isFloat({ min: 0 }).withMessage('Max capacity must be positive'),
    body('specifications.accuracy').isFloat({ min: 0, max: 100 }).withMessage('Accuracy must be between 0 and 100'),
    body('specifications.manufacturer').optional().isString().withMessage('Manufacturer must be a string'),
    body('specifications.model').optional().isString().withMessage('Model must be a string'),
    body('specifications.firmwareVersion').optional().isString().withMessage('Firmware version must be a string')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const deviceData = {
        ...req.body,
        owner: walletAddress
      }

      const device = await iotService.registerDevice(deviceData)
      res.status(201).json({ success: true, data: device })
    } catch (error) {
      console.error('Error registering device:', error)
      res.status(500).json({ error: 'Failed to register device' })
    }
  }
)

/**
 * GET /api/iot/devices
 * Get user's registered devices
 */
router.get('/devices',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const devices = await iotService.getUserDevices(walletAddress)
      res.json({ success: true, data: devices })
    } catch (error) {
      console.error('Error fetching devices:', error)
      res.status(500).json({ error: 'Failed to fetch devices' })
    }
  }
)

/**
 * POST /api/iot/readings
 * Submit a new IoT reading
 */
router.post('/readings',
  oracleRateLimiter,
  [
    body('deviceId').isString().notEmpty().withMessage('Device ID is required'),
    body('timestamp').isISO8601().withMessage('Timestamp must be valid ISO 8601 date'),
    body('readingType').isIn(['production', 'consumption', 'battery_level', 'grid_export', 'grid_import']).withMessage('Invalid reading type'),
    body('value').isFloat({ min: 0 }).withMessage('Value must be non-negative'),
    body('unit').isIn(['kWh', 'kW', 'V', 'A', '%']).withMessage('Invalid unit'),
    body('quality').optional().isIn(['good', 'fair', 'poor']).withMessage('Invalid quality'),
    body('signature').isString().notEmpty().withMessage('Signature is required')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { deviceId, timestamp, readingType, value, unit, quality = 'good', signature } = req.body

      const reading = await iotService.processReading({
        deviceId,
        timestamp: new Date(timestamp),
        readingType,
        value: parseFloat(value),
        unit,
        quality,
        signature
      })

      res.status(201).json({ success: true, data: reading })
    } catch (error) {
      console.error('Error processing reading:', error)
      res.status(500).json({ error: 'Failed to process reading' })
    }
  }
)

/**
 * GET /api/iot/readings/:deviceId
 * Get readings for a specific device
 */
router.get('/readings/:deviceId',
  authenticateWallet,
  [
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date'),
    query('readingType').optional().isIn(['production', 'consumption', 'battery_level', 'grid_export', 'grid_import']).withMessage('Invalid reading type'),
    query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('Limit must be between 1 and 1000'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { deviceId } = req.params
      const {
        startDate,
        endDate,
        readingType,
        limit = 100,
        offset = 0
      } = req.query

      const options = {
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        readingType: readingType as string,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      }

      const result = await iotService.getDeviceReadings(deviceId, options)
      res.json({ success: true, data: result })
    } catch (error) {
      console.error('Error fetching device readings:', error)
      res.status(500).json({ error: 'Failed to fetch device readings' })
    }
  }
)

/**
 * GET /api/iot/aggregated/:deviceId
 * Get aggregated data for a device
 */
router.get('/aggregated/:deviceId',
  authenticateWallet,
  [
    query('period').isIn(['1h', '24h', '7d', '30d']).withMessage('Invalid period')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { deviceId } = req.params
      const { period } = req.query

      const aggregatedData = await iotService.getAggregatedData(
        deviceId,
        period as '1h' | '24h' | '7d' | '30d'
      )

      res.json({ success: true, data: aggregatedData })
    } catch (error) {
      console.error('Error fetching aggregated data:', error)
      res.status(500).json({ error: 'Failed to fetch aggregated data' })
    }
  }
)

/**
 * GET /api/iot/alerts/:deviceId
 * Get alerts for a specific device
 */
router.get('/alerts/:deviceId',
  authenticateWallet,
  [
    query('acknowledged').optional().isBoolean().withMessage('Acknowledged must be a boolean')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { deviceId } = req.params
      const { acknowledged } = req.query

      const alerts = await iotService.getDeviceAlerts(
        deviceId,
        acknowledged !== undefined ? acknowledged === 'true' : undefined
      )

      res.json({ success: true, data: alerts })
    } catch (error) {
      console.error('Error fetching device alerts:', error)
      res.status(500).json({ error: 'Failed to fetch device alerts' })
    }
  }
)

/**
 * POST /api/iot/alerts/:alertId/acknowledge
 * Acknowledge an alert
 */
router.post('/alerts/:alertId/acknowledge',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { alertId } = req.params

      await iotService.acknowledgeAlert(alertId, walletAddress)
      res.json({ success: true, message: 'Alert acknowledged successfully' })
    } catch (error) {
      console.error('Error acknowledging alert:', error)
      res.status(500).json({ error: 'Failed to acknowledge alert' })
    }
  }
)

/**
 * GET /api/iot/device-status
 * Get status of all user devices
 */
router.get('/device-status',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const devices = await iotService.getUserDevices(walletAddress)
      
      const deviceStatus = devices.map(device => ({
        deviceId: device.deviceId,
        deviceType: device.deviceType,
        connectionStatus: device.connectionStatus,
        lastSeen: device.lastSeen,
        isVerified: device.isVerified
      }))

      res.json({ success: true, data: deviceStatus })
    } catch (error) {
      console.error('Error fetching device status:', error)
      res.status(500).json({ error: 'Failed to fetch device status' })
    }
  }
)

/**
 * POST /api/iot/simulate-reading
 * Simulate a device reading (for testing purposes)
 */
router.post('/simulate-reading',
  authenticateWallet,
  rateLimiter,
  [
    body('deviceId').isString().notEmpty().withMessage('Device ID is required'),
    body('readingType').isIn(['production', 'consumption', 'battery_level', 'grid_export', 'grid_import']).withMessage('Invalid reading type'),
    body('baseValue').optional().isFloat({ min: 0 }).withMessage('Base value must be non-negative'),
    body('variance').optional().isFloat({ min: 0, max: 100 }).withMessage('Variance must be between 0 and 100')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { deviceId, readingType, baseValue = 10, variance = 20 } = req.body

      // Generate simulated reading
      const randomVariance = (Math.random() - 0.5) * 2 * (variance / 100)
      const simulatedValue = Math.max(0, baseValue * (1 + randomVariance))

      const reading = await iotService.processReading({
        deviceId,
        timestamp: new Date(),
        readingType,
        value: simulatedValue,
        unit: readingType.includes('battery') ? '%' : 'kWh',
        quality: 'good',
        signature: `simulated_${Date.now()}_${Math.random().toString(36).substring(7)}`
      })

      res.status(201).json({ 
        success: true, 
        data: reading,
        simulation: {
          baseValue,
          variance,
          generatedValue: simulatedValue
        }
      })
    } catch (error) {
      console.error('Error simulating reading:', error)
      res.status(500).json({ error: 'Failed to simulate reading' })
    }
  }
)

/**
 * GET /api/iot/dashboard
 * Get IoT dashboard data for user
 */
router.get('/dashboard',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const devices = await iotService.getUserDevices(walletAddress)
      
      // Get recent readings for each device
      const dashboardData = await Promise.all(
        devices.map(async (device) => {
          const recentReadings = await iotService.getDeviceReadings(device.deviceId, {
            limit: 10,
            offset: 0
          })
          
          const alerts = await iotService.getDeviceAlerts(device.deviceId, false)
          
          return {
            device: {
              deviceId: device.deviceId,
              deviceType: device.deviceType,
              connectionStatus: device.connectionStatus,
              lastSeen: device.lastSeen,
              isVerified: device.isVerified
            },
            recentReadings: recentReadings.readings.slice(0, 5),
            unacknowledgedAlerts: alerts.filter(alert => !alert.acknowledged).length,
            totalReadings: recentReadings.total
          }
        })
      )

      const summary = {
        totalDevices: devices.length,
        onlineDevices: devices.filter(d => d.connectionStatus === 'online').length,
        verifiedDevices: devices.filter(d => d.isVerified).length,
        totalAlerts: dashboardData.reduce((sum, d) => sum + d.unacknowledgedAlerts, 0)
      }

      res.json({ 
        success: true, 
        data: {
          summary,
          devices: dashboardData
        }
      })
    } catch (error) {
      console.error('Error fetching IoT dashboard:', error)
      res.status(500).json({ error: 'Failed to fetch IoT dashboard' })
    }
  }
)

export default router
