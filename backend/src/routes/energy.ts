import express from 'express'
import { body, query, validationResult } from 'express-validator'
import { EnergyService } from '../services/energyService'
import { authenticateWallet } from '../middleware/auth'
import { rateLimiter } from '../middleware/rateLimiter'

const router = express.Router()
const energyService = new EnergyService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    })
  }
  next()
}

/**
 * GET /api/energy/stats
 * Get user's energy statistics
 */
router.get('/stats', 
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const stats = await energyService.getUserEnergyStats(walletAddress)
      res.json({ success: true, data: stats })
    } catch (error) {
      console.error('Error fetching energy stats:', error)
      res.status(500).json({ error: 'Failed to fetch energy statistics' })
    }
  }
)

/**
 * GET /api/energy/readings
 * Get energy meter readings for a user
 */
router.get('/readings',
  authenticateWallet,
  [
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
    query('type').optional().isIn(['production', 'consumption']).withMessage('Type must be production or consumption'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { limit = 50, offset = 0, type, startDate, endDate } = req.query
      
      const readings = await energyService.getEnergyReadings(walletAddress, {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        type: type as 'production' | 'consumption' | undefined,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined
      })

      res.json({ success: true, data: readings })
    } catch (error) {
      console.error('Error fetching energy readings:', error)
      res.status(500).json({ error: 'Failed to fetch energy readings' })
    }
  }
)

/**
 * POST /api/energy/readings
 * Submit a new energy meter reading
 */
router.post('/readings',
  authenticateWallet,
  rateLimiter,
  [
    body('meterId').isString().notEmpty().withMessage('Meter ID is required'),
    body('value').isFloat({ min: 0 }).withMessage('Value must be a positive number'),
    body('type').isIn(['production', 'consumption']).withMessage('Type must be production or consumption'),
    body('timestamp').optional().isISO8601().withMessage('Timestamp must be valid ISO 8601 date'),
    body('signature').isString().notEmpty().withMessage('Cryptographic signature is required')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { meterId, value, type, timestamp, signature } = req.body

      const reading = await energyService.submitEnergyReading({
        walletAddress,
        meterId,
        value: parseFloat(value),
        type,
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        signature
      })

      res.status(201).json({ success: true, data: reading })
    } catch (error) {
      console.error('Error submitting energy reading:', error)
      res.status(500).json({ error: 'Failed to submit energy reading' })
    }
  }
)

/**
 * GET /api/energy/balance
 * Get user's energy credit balance
 */
router.get('/balance',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const balance = await energyService.getEnergyBalance(walletAddress)
      res.json({ success: true, data: { balance } })
    } catch (error) {
      console.error('Error fetching energy balance:', error)
      res.status(500).json({ error: 'Failed to fetch energy balance' })
    }
  }
)

/**
 * POST /api/energy/mint
 * Mint energy credits from verified production
 */
router.post('/mint',
  authenticateWallet,
  rateLimiter,
  [
    body('amount').isFloat({ min: 0.001 }).withMessage('Amount must be greater than 0.001'),
    body('readingIds').isArray({ min: 1 }).withMessage('At least one reading ID is required'),
    body('readingIds.*').isString().notEmpty().withMessage('Reading IDs must be valid strings')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { amount, readingIds } = req.body

      const result = await energyService.mintEnergyCredits({
        walletAddress,
        amount: parseFloat(amount),
        readingIds
      })

      res.status(201).json({ success: true, data: result })
    } catch (error) {
      console.error('Error minting energy credits:', error)
      res.status(500).json({ error: 'Failed to mint energy credits' })
    }
  }
)

export default router
