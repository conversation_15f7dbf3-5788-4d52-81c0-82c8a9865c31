import express from 'express'
import { body, validationResult } from 'express-validator'
import { AuthService } from '../services/authService'
import { generateWalletToken } from '../middleware/auth'
import { authRateLimiter } from '../middleware/rateLimiter'
import { ValidationError } from '../middleware/errorHandler'

const router = express.Router()
const authService = new AuthService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    })
  }
  next()
}

/**
 * POST /api/auth/wallet-login
 * Authenticate using wallet signature
 */
router.post('/wallet-login',
  authRateLimiter,
  [
    body('walletAddress').isString().notEmpty().withMessage('Wallet address is required'),
    body('signature').isString().notEmpty().withMessage('Signature is required'),
    body('timestamp').isInt().withMessage('Timestamp must be an integer'),
    body('nonce').isString().notEmpty().withMessage('Nonce is required')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { walletAddress, signature, timestamp, nonce } = req.body

      // Validate the challenge response
      const isValid = await authService.validateChallengeResponse(
        walletAddress,
        nonce,
        signature,
        timestamp
      )

      if (!isValid) {
        return res.status(401).json({ 
          error: 'Invalid authentication credentials' 
        })
      }

      // Generate JWT token
      const token = await generateWalletToken(walletAddress)

      // Update user last login
      await authService.updateUserLastLogin(walletAddress)

      res.json({ 
        success: true, 
        data: { 
          token,
          walletAddress,
          expiresIn: '24h'
        } 
      })

    } catch (error) {
      console.error('Error in wallet login:', error)
      res.status(500).json({ error: 'Authentication failed' })
    }
  }
)

/**
 * POST /api/auth/challenge
 * Get authentication challenge for wallet
 */
router.post('/challenge',
  authRateLimiter,
  [
    body('walletAddress').isString().notEmpty().withMessage('Wallet address is required')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { walletAddress } = req.body

      // Generate nonce and challenge message
      const nonce = authService.generateNonce()
      const challengeMessage = authService.generateChallengeMessage(walletAddress, nonce)

      res.json({
        success: true,
        data: {
          nonce,
          challengeMessage,
          expiresIn: 300 // 5 minutes
        }
      })

    } catch (error) {
      console.error('Error generating challenge:', error)
      res.status(500).json({ error: 'Failed to generate challenge' })
    }
  }
)

/**
 * POST /api/auth/logout
 * Logout and invalidate session
 */
router.post('/logout',
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        
        // Extract session ID from token and invalidate
        try {
          const jwt = require('jsonwebtoken')
          const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any
          await authService.invalidateSession(decoded.sessionId)
        } catch (jwtError) {
          // Token might be invalid, but that's okay for logout
        }
      }

      res.json({ 
        success: true, 
        message: 'Logged out successfully' 
      })

    } catch (error) {
      console.error('Error in logout:', error)
      res.status(500).json({ error: 'Logout failed' })
    }
  }
)

/**
 * GET /api/auth/profile
 * Get user authentication profile
 */
router.get('/profile',
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authentication required' })
      }

      const token = authHeader.substring(7)
      const jwt = require('jsonwebtoken')
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any

      // Validate session
      const isValidSession = await authService.validateSession(decoded.sessionId)
      if (!isValidSession) {
        return res.status(401).json({ error: 'Session expired' })
      }

      // Get user auth info
      const authInfo = await authService.getUserAuthInfo(decoded.walletAddress)

      res.json({
        success: true,
        data: {
          walletAddress: decoded.walletAddress,
          sessionId: decoded.sessionId,
          issuedAt: new Date(decoded.iat * 1000),
          expiresAt: new Date(decoded.exp * 1000),
          ...authInfo
        }
      })

    } catch (error) {
      console.error('Error fetching profile:', error)
      res.status(401).json({ error: 'Invalid token' })
    }
  }
)

/**
 * POST /api/auth/refresh
 * Refresh authentication token
 */
router.post('/refresh',
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Authentication required' })
      }

      const token = authHeader.substring(7)
      const jwt = require('jsonwebtoken')
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any
        
        // Check if token is close to expiry (within 1 hour)
        const now = Math.floor(Date.now() / 1000)
        const timeToExpiry = decoded.exp - now
        
        if (timeToExpiry > 3600) { // More than 1 hour left
          return res.json({
            success: true,
            data: { token, message: 'Token still valid' }
          })
        }

        // Generate new token
        const newToken = await generateWalletToken(decoded.walletAddress)

        res.json({
          success: true,
          data: { 
            token: newToken,
            walletAddress: decoded.walletAddress,
            expiresIn: '24h'
          }
        })

      } catch (jwtError) {
        return res.status(401).json({ error: 'Invalid token' })
      }

    } catch (error) {
      console.error('Error refreshing token:', error)
      res.status(500).json({ error: 'Token refresh failed' })
    }
  }
)

/**
 * GET /api/auth/status
 * Check authentication status
 */
router.get('/status',
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.json({
          success: true,
          data: { 
            isAuthenticated: false,
            message: 'No authentication token provided'
          }
        })
      }

      const token = authHeader.substring(7)
      const jwt = require('jsonwebtoken')
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any
        
        // Validate session
        const isValidSession = await authService.validateSession(decoded.sessionId)
        
        res.json({
          success: true,
          data: {
            isAuthenticated: isValidSession,
            walletAddress: isValidSession ? decoded.walletAddress : null,
            expiresAt: isValidSession ? new Date(decoded.exp * 1000) : null
          }
        })

      } catch (jwtError) {
        res.json({
          success: true,
          data: {
            isAuthenticated: false,
            message: 'Invalid token'
          }
        })
      }

    } catch (error) {
      console.error('Error checking auth status:', error)
      res.status(500).json({ error: 'Status check failed' })
    }
  }
)

export default router
