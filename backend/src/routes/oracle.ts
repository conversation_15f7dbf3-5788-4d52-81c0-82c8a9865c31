import express from 'express'
import { body, query, validationResult } from 'express-validator'
import { OracleService } from '../services/oracleService'
import { authenticateWallet } from '../middleware/auth'
import { rateLimiter } from '../middleware/rateLimiter'

const router = express.Router()
const oracleService = new OracleService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    })
  }
  next()
}

/**
 * POST /api/oracle/register-meter
 * Register a new energy meter
 */
router.post('/register-meter',
  authenticateWallet,
  rateLimiter,
  [
    body('meterId').isString().notEmpty().withMessage('Meter ID is required'),
    body('meterType').isIn(['production', 'consumption', 'bidirectional']).withMessage('Invalid meter type'),
    body('location').isObject().withMessage('Location must be an object'),
    body('location.latitude').isFloat({ min: -90, max: 90 }).withMessage('Invalid latitude'),
    body('location.longitude').isFloat({ min: -180, max: 180 }).withMessage('Invalid longitude'),
    body('location.address').isString().notEmpty().withMessage('Address is required'),
    body('specifications').isObject().withMessage('Specifications must be an object'),
    body('specifications.maxCapacity').isFloat({ min: 0 }).withMessage('Max capacity must be positive'),
    body('specifications.accuracy').isFloat({ min: 0, max: 100 }).withMessage('Accuracy must be between 0 and 100'),
    body('publicKey').isString().notEmpty().withMessage('Public key for signature verification is required')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { 
        meterId, 
        meterType, 
        location, 
        specifications, 
        publicKey 
      } = req.body

      const meter = await oracleService.registerMeter({
        owner: walletAddress,
        meterId,
        meterType,
        location,
        specifications,
        publicKey
      })

      res.status(201).json({ success: true, data: meter })
    } catch (error) {
      console.error('Error registering meter:', error)
      res.status(500).json({ error: 'Failed to register meter' })
    }
  }
)

/**
 * GET /api/oracle/meters
 * Get user's registered meters
 */
router.get('/meters',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const meters = await oracleService.getUserMeters(walletAddress)
      res.json({ success: true, data: meters })
    } catch (error) {
      console.error('Error fetching meters:', error)
      res.status(500).json({ error: 'Failed to fetch meters' })
    }
  }
)

/**
 * POST /api/oracle/submit-reading
 * Submit a meter reading to the oracle
 */
router.post('/submit-reading',
  rateLimiter,
  [
    body('meterId').isString().notEmpty().withMessage('Meter ID is required'),
    body('reading').isObject().withMessage('Reading must be an object'),
    body('reading.value').isFloat({ min: 0 }).withMessage('Reading value must be positive'),
    body('reading.timestamp').isISO8601().withMessage('Timestamp must be valid ISO 8601 date'),
    body('reading.unit').isIn(['kWh', 'MWh', 'GWh']).withMessage('Invalid unit'),
    body('signature').isString().notEmpty().withMessage('Cryptographic signature is required'),
    body('nonce').isString().notEmpty().withMessage('Nonce is required for replay protection')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { meterId, reading, signature, nonce } = req.body

      const result = await oracleService.submitReading({
        meterId,
        reading: {
          value: parseFloat(reading.value),
          timestamp: new Date(reading.timestamp),
          unit: reading.unit
        },
        signature,
        nonce
      })

      res.status(201).json({ success: true, data: result })
    } catch (error) {
      console.error('Error submitting reading:', error)
      res.status(500).json({ error: 'Failed to submit reading' })
    }
  }
)

/**
 * GET /api/oracle/readings/:meterId
 * Get readings for a specific meter
 */
router.get('/readings/:meterId',
  authenticateWallet,
  [
    query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('Limit must be between 1 and 1000'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
    query('startDate').optional().isISO8601().withMessage('Start date must be valid ISO 8601 date'),
    query('endDate').optional().isISO8601().withMessage('End date must be valid ISO 8601 date'),
    query('verified').optional().isBoolean().withMessage('Verified must be a boolean')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { meterId } = req.params
      const { 
        limit = 100, 
        offset = 0, 
        startDate, 
        endDate, 
        verified 
      } = req.query

      const readings = await oracleService.getMeterReadings(meterId, walletAddress, {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        verified: verified !== undefined ? verified === 'true' : undefined
      })

      res.json({ success: true, data: readings })
    } catch (error) {
      console.error('Error fetching meter readings:', error)
      res.status(500).json({ error: 'Failed to fetch meter readings' })
    }
  }
)

/**
 * POST /api/oracle/verify-reading
 * Verify a submitted reading (oracle validator endpoint)
 */
router.post('/verify-reading',
  [
    body('readingId').isString().notEmpty().withMessage('Reading ID is required'),
    body('validatorSignature').isString().notEmpty().withMessage('Validator signature is required'),
    body('validatorAddress').isString().notEmpty().withMessage('Validator address is required'),
    body('isValid').isBoolean().withMessage('Validation result must be a boolean'),
    body('validationNotes').optional().isString().withMessage('Validation notes must be a string')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { 
        readingId, 
        validatorSignature, 
        validatorAddress, 
        isValid, 
        validationNotes 
      } = req.body

      const result = await oracleService.verifyReading({
        readingId,
        validatorSignature,
        validatorAddress,
        isValid,
        validationNotes
      })

      res.json({ success: true, data: result })
    } catch (error) {
      console.error('Error verifying reading:', error)
      res.status(500).json({ error: 'Failed to verify reading' })
    }
  }
)

/**
 * GET /api/oracle/validation-status/:readingId
 * Get validation status for a reading
 */
router.get('/validation-status/:readingId',
  async (req, res) => {
    try {
      const { readingId } = req.params

      const status = await oracleService.getValidationStatus(readingId)
      res.json({ success: true, data: status })
    } catch (error) {
      console.error('Error fetching validation status:', error)
      res.status(500).json({ error: 'Failed to fetch validation status' })
    }
  }
)

/**
 * GET /api/oracle/stats
 * Get oracle network statistics
 */
router.get('/stats',
  async (req, res) => {
    try {
      const stats = await oracleService.getOracleStats()
      res.json({ success: true, data: stats })
    } catch (error) {
      console.error('Error fetching oracle stats:', error)
      res.status(500).json({ error: 'Failed to fetch oracle statistics' })
    }
  }
)

export default router
