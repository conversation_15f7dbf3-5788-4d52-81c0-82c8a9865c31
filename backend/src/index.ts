import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import dotenv from 'dotenv'
import { createServer } from 'http'
import { WebSocketServer } from 'ws'

import { errorHandler } from './middleware/errorHandler'
import { rateLimiter } from './middleware/rateLimiter'
import authRoutes from './routes/auth'
import energyRoutes from './routes/energy'
import marketRoutes from './routes/market'
import oracleRoutes from './routes/oracle'
import analyticsRoutes from './routes/analytics'
import iotRoutes from './routes/iot'
import historyRoutes from './routes/history'
import { DatabaseService } from './services/databaseService'
import { MasChainService } from './services/masChainService'
import { WebSocketManager } from './services/websocket'
import { OrderBookService } from './services/orderBookService'
import { IoTService } from './services/iotService'
import { AnalyticsService } from './services/analyticsService'
import { TransactionHistoryService } from './services/transactionHistoryService'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(helmet())
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}))
app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))
app.use(rateLimiter)

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '0.1.0'
  })
})

// API Routes
app.use('/api/auth', authRoutes)
app.use('/api/energy', energyRoutes)
app.use('/api/market', marketRoutes)
app.use('/api/oracle', oracleRoutes)
app.use('/api/analytics', analyticsRoutes)
app.use('/api/iot', iotRoutes)
app.use('/api/history', historyRoutes)

// Error handling
app.use(errorHandler)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

// Create HTTP server
const server = createServer(app)

// Initialize WebSocket server
const wss = new WebSocketServer({ server })
const wsManager = new WebSocketManager(wss)

async function startServer() {
  try {
    console.log('🚀 Starting MasChain Energy Trading Platform - Phase 2')
    console.log('=' .repeat(60))

    // Initialize database
    const databaseService = new DatabaseService()
    await databaseService.initialize()
    console.log('✅ Database connected')

    // Initialize blockchain connection
    const masChainService = new MasChainService()
    await masChainService.initialize()
    console.log('✅ Blockchain connected')

    // Initialize order book service
    const orderBookService = new OrderBookService()
    await orderBookService.initialize()
    console.log('✅ Order Book initialized')

    // Initialize IoT service
    const iotService = new IoTService()
    await iotService.initialize()
    console.log('✅ IoT Service initialized')

    // Initialize analytics service
    const analyticsService = new AnalyticsService()
    console.log('✅ Analytics Service initialized')

    // Initialize transaction history service
    const transactionHistoryService = new TransactionHistoryService()
    console.log('✅ Transaction History Service initialized')

    // Start server
    server.listen(PORT, () => {
      console.log('\n' + '=' .repeat(60))
      console.log(`🚀 Server running on port ${PORT}`)
      console.log(`📊 Health check: http://localhost:${PORT}/health`)
      console.log(`🔌 WebSocket server ready`)
      console.log(`📡 API endpoints:`)
      console.log(`   - Auth: http://localhost:${PORT}/api/auth`)
      console.log(`   - Energy: http://localhost:${PORT}/api/energy`)
      console.log(`   - Market: http://localhost:${PORT}/api/market`)
      console.log(`   - Oracle: http://localhost:${PORT}/api/oracle`)
      console.log(`   - Analytics: http://localhost:${PORT}/api/analytics`)
      console.log(`   - IoT: http://localhost:${PORT}/api/iot`)
      console.log('\n🎉 Phase 2 Features Active:')
      console.log('   ✅ Real-time Order Book & Matching Engine')
      console.log('   ✅ IoT Device Integration & MQTT')
      console.log('   ✅ Advanced Analytics & Forecasting')
      console.log('   ✅ Transaction History & Audit Trail')
      console.log('   ✅ Carbon Footprint Tracking')
      console.log('   ✅ Performance Metrics & Insights')
      console.log('=' .repeat(60))
    })

  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  server.close(() => {
    console.log('Process terminated')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully')
  server.close(() => {
    console.log('Process terminated')
    process.exit(0)
  })
})

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

startServer()
