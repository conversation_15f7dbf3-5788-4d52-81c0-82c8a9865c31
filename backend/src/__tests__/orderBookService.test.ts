import { OrderBookService, OrderBookEntry, TradeMatch } from '../services/orderBookService'
import { DatabaseService } from '../services/databaseService'
import { MasChainService } from '../services/masChainService'
import { WebSocketManager } from '../services/websocket'

// Mock dependencies
jest.mock('../services/databaseService')
jest.mock('../services/masChainService')
jest.mock('../services/websocket')

describe('OrderBookService', () => {
  let orderBookService: OrderBookService
  let mockDatabaseService: jest.Mocked<DatabaseService>
  let mockMasChainService: jest.Mocked<MasChainService>
  let mockWsManager: jest.Mocked<WebSocketManager>

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Create mock instances
    mockDatabaseService = new DatabaseService() as jest.Mocked<DatabaseService>
    mockMasChainService = new MasChainService() as jest.Mocked<MasChainService>
    mockWsManager = {
      broadcastMarketUpdate: jest.fn(),
      getInstance: jest.fn()
    } as any

    // Mock static method
    ;(WebSocketManager.getInstance as jest.Mock).mockReturnValue(mockWsManager)

    // Create service instance
    orderBookService = new OrderBookService()
  })

  describe('Order Management', () => {
    test('should add a new buy order to the order book', async () => {
      const orderInput = {
        offerId: 'test-offer-1',
        seller: 'buyer-wallet-address',
        energyAmount: 100,
        pricePerKwh: 0.15,
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000) // 1 hour from now
      }

      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)
      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([])

      const result = await orderBookService.addOrder(orderInput)

      expect(result).toBeDefined()
      expect(result.orderType).toBe('buy')
      expect(result.energyAmount).toBe(100)
      expect(result.pricePerKwh).toBe(0.15)
      expect(mockDatabaseService.createOrder).toHaveBeenCalledTimes(1)
    })

    test('should add a new sell order to the order book', async () => {
      const orderInput = {
        offerId: 'test-offer-2',
        seller: 'seller-wallet-address',
        energyAmount: 50,
        pricePerKwh: 0.12,
        orderType: 'sell' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)
      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([])

      const result = await orderBookService.addOrder(orderInput)

      expect(result).toBeDefined()
      expect(result.orderType).toBe('sell')
      expect(result.energyAmount).toBe(50)
      expect(result.pricePerKwh).toBe(0.12)
    })

    test('should reject order with invalid amount', async () => {
      const orderInput = {
        offerId: 'test-offer-3',
        seller: 'wallet-address',
        energyAmount: -10, // Invalid negative amount
        pricePerKwh: 0.15,
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      await expect(orderBookService.addOrder(orderInput)).rejects.toThrow()
    })

    test('should reject order with invalid price', async () => {
      const orderInput = {
        offerId: 'test-offer-4',
        seller: 'wallet-address',
        energyAmount: 100,
        pricePerKwh: -0.05, // Invalid negative price
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      await expect(orderBookService.addOrder(orderInput)).rejects.toThrow()
    })
  })

  describe('Order Matching', () => {
    test('should match buy and sell orders at compatible prices', async () => {
      // Setup existing sell order
      const sellOrder: OrderBookEntry = {
        id: 'sell-order-1',
        offerId: 'sell-offer-1',
        seller: 'seller-wallet',
        energyAmount: 100,
        remainingAmount: 100,
        pricePerKwh: 0.12,
        orderType: 'sell',
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        status: 'active'
      }

      // Mock database to return the sell order
      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([sellOrder])
      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)
      mockDatabaseService.updateOrderStatus = jest.fn().mockResolvedValue(undefined)
      mockDatabaseService.createTrade = jest.fn().mockResolvedValue(undefined)
      mockMasChainService.executeTrade = jest.fn().mockResolvedValue('tx-hash-123')

      // Initialize order book with existing sell order
      await orderBookService.initialize()

      // Add buy order that should match
      const buyOrderInput = {
        offerId: 'buy-offer-1',
        seller: 'buyer-wallet', // Note: seller field is used for wallet address
        energyAmount: 50,
        pricePerKwh: 0.15, // Higher than sell price, should match
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      const result = await orderBookService.addOrder(buyOrderInput)

      expect(result).toBeDefined()
      expect(mockMasChainService.executeTrade).toHaveBeenCalledTimes(1)
      expect(mockDatabaseService.createTrade).toHaveBeenCalledTimes(1)
    })

    test('should not match orders with incompatible prices', async () => {
      // Setup existing sell order with high price
      const sellOrder: OrderBookEntry = {
        id: 'sell-order-2',
        offerId: 'sell-offer-2',
        seller: 'seller-wallet',
        energyAmount: 100,
        remainingAmount: 100,
        pricePerKwh: 0.20, // High price
        orderType: 'sell',
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        status: 'active'
      }

      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([sellOrder])
      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)
      mockMasChainService.executeTrade = jest.fn().mockResolvedValue('tx-hash-123')

      await orderBookService.initialize()

      // Add buy order with lower price
      const buyOrderInput = {
        offerId: 'buy-offer-2',
        seller: 'buyer-wallet',
        energyAmount: 50,
        pricePerKwh: 0.15, // Lower than sell price, should not match
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      await orderBookService.addOrder(buyOrderInput)

      // Should not execute any trades
      expect(mockMasChainService.executeTrade).not.toHaveBeenCalled()
    })

    test('should handle partial fills correctly', async () => {
      // Setup large sell order
      const sellOrder: OrderBookEntry = {
        id: 'sell-order-3',
        offerId: 'sell-offer-3',
        seller: 'seller-wallet',
        energyAmount: 200,
        remainingAmount: 200,
        pricePerKwh: 0.12,
        orderType: 'sell',
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        status: 'active'
      }

      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([sellOrder])
      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)
      mockDatabaseService.updateOrderPartialFill = jest.fn().mockResolvedValue(undefined)
      mockDatabaseService.createTrade = jest.fn().mockResolvedValue(undefined)
      mockMasChainService.executeTrade = jest.fn().mockResolvedValue('tx-hash-123')

      await orderBookService.initialize()

      // Add smaller buy order
      const buyOrderInput = {
        offerId: 'buy-offer-3',
        seller: 'buyer-wallet',
        energyAmount: 75, // Partial fill
        pricePerKwh: 0.15,
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      await orderBookService.addOrder(buyOrderInput)

      expect(mockMasChainService.executeTrade).toHaveBeenCalledWith(
        expect.objectContaining({
          energyAmount: 75
        })
      )
    })

    test('should prevent self-trading', async () => {
      const sameWallet = 'same-wallet-address'

      // Setup sell order
      const sellOrder: OrderBookEntry = {
        id: 'sell-order-4',
        offerId: 'sell-offer-4',
        seller: sameWallet,
        energyAmount: 100,
        remainingAmount: 100,
        pricePerKwh: 0.12,
        orderType: 'sell',
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        status: 'active'
      }

      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([sellOrder])
      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)

      await orderBookService.initialize()

      // Try to add buy order from same wallet
      const buyOrderInput = {
        offerId: 'buy-offer-4',
        seller: sameWallet, // Same wallet address
        energyAmount: 50,
        pricePerKwh: 0.15,
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      await orderBookService.addOrder(buyOrderInput)

      // Should not execute any trades (self-trading prevention)
      expect(mockMasChainService.executeTrade).not.toHaveBeenCalled()
    })
  })

  describe('Order Book State', () => {
    test('should return correct order book snapshot', async () => {
      const orders = [
        {
          id: 'order-1',
          offerId: 'offer-1',
          seller: 'seller-1',
          energyAmount: 100,
          remainingAmount: 100,
          pricePerKwh: 0.12,
          orderType: 'sell',
          timestamp: new Date(),
          expiresAt: new Date(Date.now() + 3600000),
          status: 'active'
        },
        {
          id: 'order-2',
          offerId: 'offer-2',
          seller: 'buyer-1',
          energyAmount: 50,
          remainingAmount: 50,
          pricePerKwh: 0.15,
          orderType: 'buy',
          timestamp: new Date(),
          expiresAt: new Date(Date.now() + 3600000),
          status: 'active'
        }
      ]

      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue(orders)
      mockDatabaseService.getMarketStats24h = jest.fn().mockResolvedValue({
        volume24h: 1000,
        lastPrice: 0.13
      })

      await orderBookService.initialize()

      const snapshot = orderBookService.getOrderBookSnapshot()

      expect(snapshot).toBeDefined()
      expect(snapshot.bids).toBeDefined() // Buy orders
      expect(snapshot.asks).toBeDefined() // Sell orders
      expect(snapshot.lastPrice).toBe(0.13)
      expect(snapshot.volume24h).toBe(1000)
    })

    test('should cancel order successfully', async () => {
      const orderId = 'order-to-cancel'
      
      mockDatabaseService.updateOrderStatus = jest.fn().mockResolvedValue(undefined)

      await orderBookService.cancelOrder(orderId, 'wallet-address')

      expect(mockDatabaseService.updateOrderStatus).toHaveBeenCalledWith(orderId, 'cancelled')
    })
  })

  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      const orderInput = {
        offerId: 'test-offer-error',
        seller: 'wallet-address',
        energyAmount: 100,
        pricePerKwh: 0.15,
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      mockDatabaseService.createOrder = jest.fn().mockRejectedValue(new Error('Database error'))

      await expect(orderBookService.addOrder(orderInput)).rejects.toThrow('Database error')
    })

    test('should handle blockchain errors during trade execution', async () => {
      const sellOrder: OrderBookEntry = {
        id: 'sell-order-error',
        offerId: 'sell-offer-error',
        seller: 'seller-wallet',
        energyAmount: 100,
        remainingAmount: 100,
        pricePerKwh: 0.12,
        orderType: 'sell',
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        status: 'active'
      }

      mockDatabaseService.getActiveOrders = jest.fn().mockResolvedValue([sellOrder])
      mockDatabaseService.createOrder = jest.fn().mockResolvedValue(undefined)
      mockMasChainService.executeTrade = jest.fn().mockRejectedValue(new Error('Blockchain error'))

      await orderBookService.initialize()

      const buyOrderInput = {
        offerId: 'buy-offer-error',
        seller: 'buyer-wallet',
        energyAmount: 50,
        pricePerKwh: 0.15,
        orderType: 'buy' as const,
        expiresAt: new Date(Date.now() + 3600000)
      }

      // Should not throw, but should handle error gracefully
      await expect(orderBookService.addOrder(buyOrderInput)).resolves.toBeDefined()
    })
  })
})
