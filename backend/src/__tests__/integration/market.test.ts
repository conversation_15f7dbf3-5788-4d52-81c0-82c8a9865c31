import request from 'supertest'
import express from 'express'
import marketRoutes from '../../routes/market'
import { DatabaseService } from '../../services/databaseService'
import { MasChainService } from '../../services/masChainService'
import { OrderBookService } from '../../services/orderBookService'

// Mock dependencies
jest.mock('../../services/databaseService')
jest.mock('../../services/masChainService')
jest.mock('../../services/orderBookService')

describe('Market API Integration Tests', () => {
  let app: express.Application
  let mockDatabaseService: jest.Mocked<DatabaseService>
  let mockMasChainService: jest.Mocked<MasChainService>
  let mockOrderBookService: jest.Mocked<OrderBookService>

  beforeEach(() => {
    // Create Express app
    app = express()
    app.use(express.json())
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.user = { walletAddress: 'test-wallet-address' }
      next()
    })
    
    app.use('/api/market', marketRoutes)

    // Setup mocks
    mockDatabaseService = new DatabaseService() as jest.Mocked<DatabaseService>
    mockMasChainService = new MasChainService() as jest.Mocked<MasChainService>
    mockOrderBookService = new OrderBookService() as jest.Mocked<OrderBookService>

    jest.clearAllMocks()
  })

  describe('GET /api/market/offers', () => {
    test('should return active market offers', async () => {
      const mockOffers = {
        offers: [
          {
            id: 'offer-1',
            seller: 'seller-wallet',
            energyAmount: 100,
            pricePerKwh: 0.15,
            offerType: 'immediate',
            status: 'active',
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 3600000)
          }
        ],
        total: 1
      }

      // Mock the market service method
      const mockMarketService = {
        getActiveOffers: jest.fn().mockResolvedValue(mockOffers),
        updateExpiredOffers: jest.fn().mockResolvedValue(undefined)
      }

      // Replace the service in the route handler
      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      const response = await request(app)
        .get('/api/market/offers')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.offers).toHaveLength(1)
      expect(response.body.data.offers[0].id).toBe('offer-1')
    })

    test('should handle query parameters correctly', async () => {
      const mockOffers = { offers: [], total: 0 }
      const mockMarketService = {
        getActiveOffers: jest.fn().mockResolvedValue(mockOffers),
        updateExpiredOffers: jest.fn().mockResolvedValue(undefined)
      }

      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      await request(app)
        .get('/api/market/offers')
        .query({
          limit: '10',
          offset: '0',
          type: 'immediate',
          minPrice: '0.10',
          maxPrice: '0.20'
        })
        .expect(200)

      expect(mockMarketService.getActiveOffers).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        type: 'immediate',
        priceRange: { min: 0.10, max: 0.20 },
        amountRange: undefined
      })
    })

    test('should return 500 on service error', async () => {
      const mockMarketService = {
        getActiveOffers: jest.fn().mockRejectedValue(new Error('Service error')),
        updateExpiredOffers: jest.fn().mockResolvedValue(undefined)
      }

      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      await request(app)
        .get('/api/market/offers')
        .expect(500)
    })
  })

  describe('POST /api/market/offers', () => {
    test('should create a new energy offer', async () => {
      const mockOffer = {
        id: 'new-offer-1',
        seller: 'test-wallet-address',
        energyAmount: 100,
        pricePerKwh: 0.15,
        offerType: 'immediate',
        status: 'active',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000)
      }

      const mockMarketService = {
        createOffer: jest.fn().mockResolvedValue(mockOffer)
      }

      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      const offerData = {
        energyAmount: 100,
        pricePerKwh: 0.15,
        offerType: 'immediate',
        duration: 24
      }

      const response = await request(app)
        .post('/api/market/offers')
        .send(offerData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.id).toBe('new-offer-1')
      expect(mockMarketService.createOffer).toHaveBeenCalledWith({
        seller: 'test-wallet-address',
        ...offerData
      })
    })

    test('should validate required fields', async () => {
      const invalidData = {
        energyAmount: 100
        // Missing required fields
      }

      await request(app)
        .post('/api/market/offers')
        .send(invalidData)
        .expect(400)
    })

    test('should validate energy amount is positive', async () => {
      const invalidData = {
        energyAmount: -10,
        pricePerKwh: 0.15,
        offerType: 'immediate',
        duration: 24
      }

      await request(app)
        .post('/api/market/offers')
        .send(invalidData)
        .expect(400)
    })

    test('should validate price is positive', async () => {
      const invalidData = {
        energyAmount: 100,
        pricePerKwh: -0.05,
        offerType: 'immediate',
        duration: 24
      }

      await request(app)
        .post('/api/market/offers')
        .send(invalidData)
        .expect(400)
    })
  })

  describe('POST /api/market/trade', () => {
    test('should execute a trade successfully', async () => {
      const mockTrade = {
        id: 'trade-1',
        buyer: 'test-wallet-address',
        seller: 'seller-wallet',
        offerId: 'offer-1',
        amount: 50,
        pricePerKwh: 0.15,
        totalPrice: 7.5,
        status: 'completed',
        executedAt: new Date()
      }

      const mockMarketService = {
        executeTrade: jest.fn().mockResolvedValue(mockTrade)
      }

      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      const tradeData = {
        offerId: 'offer-1',
        amount: 50
      }

      const response = await request(app)
        .post('/api/market/trade')
        .send(tradeData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.id).toBe('trade-1')
      expect(mockMarketService.executeTrade).toHaveBeenCalledWith({
        buyer: 'test-wallet-address',
        ...tradeData
      })
    })

    test('should validate trade amount is positive', async () => {
      const invalidData = {
        offerId: 'offer-1',
        amount: -10
      }

      await request(app)
        .post('/api/market/trade')
        .send(invalidData)
        .expect(400)
    })

    test('should handle insufficient balance error', async () => {
      const mockMarketService = {
        executeTrade: jest.fn().mockRejectedValue(new Error('Insufficient balance'))
      }

      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      const tradeData = {
        offerId: 'offer-1',
        amount: 50
      }

      await request(app)
        .post('/api/market/trade')
        .send(tradeData)
        .expect(500)
    })
  })

  describe('GET /api/market/stats', () => {
    test('should return market statistics', async () => {
      const mockStats = {
        totalOffers: 25,
        totalVolumeTraded: 1500,
        averagePrice: 0.14,
        priceChange24h: 5.2,
        volume24h: 300,
        activeBuyers: 12,
        activeSellers: 8
      }

      const mockMarketService = {
        getMarketStats: jest.fn().mockResolvedValue(mockStats)
      }

      jest.doMock('../../services/marketService', () => ({
        MarketService: jest.fn(() => mockMarketService)
      }))

      const response = await request(app)
        .get('/api/market/stats')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.totalOffers).toBe(25)
      expect(response.body.data.averagePrice).toBe(0.14)
    })
  })

  describe('GET /api/market/orderbook', () => {
    test('should return order book data', async () => {
      const mockOrderBook = {
        bids: [
          { id: 'bid-1', pricePerKwh: 0.15, remainingAmount: 100 }
        ],
        asks: [
          { id: 'ask-1', pricePerKwh: 0.12, remainingAmount: 50 }
        ],
        lastPrice: 0.14,
        volume24h: 500,
        priceChange24h: 2.1,
        timestamp: new Date()
      }

      mockOrderBookService.getOrderBookSnapshot = jest.fn().mockReturnValue(mockOrderBook)

      // Mock the singleton instance
      jest.doMock('../../services/orderBookService', () => ({
        OrderBookService: {
          getInstance: jest.fn(() => mockOrderBookService)
        }
      }))

      const response = await request(app)
        .get('/api/market/orderbook')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.bids).toHaveLength(1)
      expect(response.body.data.asks).toHaveLength(1)
    })
  })

  describe('POST /api/market/orders', () => {
    test('should place a limit order', async () => {
      const mockOrder = {
        id: 'order-1',
        offerId: 'offer-1',
        seller: 'test-wallet-address',
        energyAmount: 100,
        pricePerKwh: 0.15,
        orderType: 'buy',
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 3600000),
        status: 'active'
      }

      mockOrderBookService.addOrder = jest.fn().mockResolvedValue(mockOrder)

      jest.doMock('../../services/orderBookService', () => ({
        OrderBookService: {
          getInstance: jest.fn(() => mockOrderBookService)
        }
      }))

      const orderData = {
        energyAmount: 100,
        pricePerKwh: 0.15,
        orderType: 'buy',
        expiresIn: 1 // 1 hour
      }

      const response = await request(app)
        .post('/api/market/orders')
        .send(orderData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.id).toBe('order-1')
    })

    test('should validate order type', async () => {
      const invalidData = {
        energyAmount: 100,
        pricePerKwh: 0.15,
        orderType: 'invalid',
        expiresIn: 1
      }

      await request(app)
        .post('/api/market/orders')
        .send(invalidData)
        .expect(400)
    })
  })

  describe('Authentication', () => {
    test('should require authentication for protected endpoints', async () => {
      // Create app without authentication middleware
      const unauthApp = express()
      unauthApp.use(express.json())
      unauthApp.use('/api/market', marketRoutes)

      await request(unauthApp)
        .post('/api/market/offers')
        .send({
          energyAmount: 100,
          pricePerKwh: 0.15,
          offerType: 'immediate',
          duration: 24
        })
        .expect(401)
    })
  })
})
