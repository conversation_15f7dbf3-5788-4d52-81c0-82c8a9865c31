import { DatabaseService } from './databaseService'
import { MasChainService } from './masChainService'
import { WebSocketManager } from './websocket'

export interface TransactionRecord {
  id: string
  transactionHash: string
  blockNumber: number
  blockHash: string
  transactionIndex: number
  from: string
  to: string
  value: string
  gasUsed: number
  gasPrice: string
  timestamp: Date
  status: 'pending' | 'confirmed' | 'failed'
  transactionType: 'energy_trade' | 'token_mint' | 'token_transfer' | 'offer_creation' | 'offer_cancellation'
  metadata: any
  confirmations: number
}

export interface UserActivity {
  id: string
  userId: string
  activityType: 'login' | 'logout' | 'trade' | 'offer_created' | 'offer_cancelled' | 'reading_submitted' | 'device_registered'
  description: string
  metadata: any
  ipAddress?: string
  userAgent?: string
  timestamp: Date
}

export interface TradeHistory {
  id: string
  buyer: string
  seller: string
  energyAmount: number
  pricePerKwh: number
  totalPrice: number
  tradingFee: number
  netAmount: number
  executedAt: Date
  transactionHash: string
  status: 'completed' | 'pending' | 'failed'
  orderIds: string[]
}

export interface PerformanceMetrics {
  userId: string
  period: '24h' | '7d' | '30d' | '1y'
  totalTrades: number
  totalVolume: number
  totalRevenue: number
  totalCosts: number
  netProfit: number
  averageTradeSize: number
  successRate: number
  carbonOffset: number
  energyEfficiency: number
  timestamp: Date
}

export interface AuditTrail {
  id: string
  entityType: 'user' | 'device' | 'trade' | 'offer' | 'reading'
  entityId: string
  action: 'create' | 'update' | 'delete' | 'verify' | 'cancel'
  oldValues?: any
  newValues?: any
  performedBy: string
  timestamp: Date
  ipAddress?: string
  reason?: string
}

export interface TransactionQuery {
  userId?: string
  transactionType?: string
  status?: string
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
  sortBy?: 'timestamp' | 'value' | 'gasUsed'
  sortOrder?: 'asc' | 'desc'
}

export class TransactionHistoryService {
  private databaseService: DatabaseService
  private masChainService: MasChainService
  private wsManager: WebSocketManager

  constructor() {
    this.databaseService = new DatabaseService()
    this.masChainService = new MasChainService()
    this.wsManager = WebSocketManager.getInstance()
    
    // Start periodic tasks
    setInterval(() => this.updatePendingTransactions(), 30000) // Every 30 seconds
    setInterval(() => this.calculatePerformanceMetrics(), 300000) // Every 5 minutes
  }

  /**
   * Record a new transaction
   */
  async recordTransaction(transaction: Omit<TransactionRecord, 'id' | 'timestamp' | 'confirmations'>): Promise<TransactionRecord> {
    try {
      const record: TransactionRecord = {
        ...transaction,
        id: this.generateTransactionId(),
        timestamp: new Date(),
        confirmations: 0
      }

      await this.databaseService.createTransactionRecord(record)
      
      console.log(`📝 Transaction recorded: ${record.transactionHash} (${record.transactionType})`)
      
      // Broadcast to WebSocket clients
      this.wsManager.broadcastMarketUpdate('transaction_recorded', record)
      
      return record
    } catch (error) {
      console.error('Error recording transaction:', error)
      throw error
    }
  }

  /**
   * Log user activity
   */
  async logActivity(activity: Omit<UserActivity, 'id' | 'timestamp'>): Promise<UserActivity> {
    try {
      const record: UserActivity = {
        ...activity,
        id: this.generateActivityId(),
        timestamp: new Date()
      }

      await this.databaseService.createUserActivity(record)
      
      console.log(`👤 Activity logged: ${record.userId} - ${record.activityType}`)
      
      return record
    } catch (error) {
      console.error('Error logging activity:', error)
      throw error
    }
  }

  /**
   * Record a completed trade
   */
  async recordTrade(trade: Omit<TradeHistory, 'id'>): Promise<TradeHistory> {
    try {
      const record: TradeHistory = {
        ...trade,
        id: this.generateTradeId()
      }

      await this.databaseService.createTradeHistory(record)
      
      // Log activities for both parties
      await this.logActivity({
        userId: trade.buyer,
        activityType: 'trade',
        description: `Purchased ${trade.energyAmount} kWh for ${trade.totalPrice} credits`,
        metadata: { tradeId: record.id, role: 'buyer' }
      })

      await this.logActivity({
        userId: trade.seller,
        activityType: 'trade',
        description: `Sold ${trade.energyAmount} kWh for ${trade.totalPrice} credits`,
        metadata: { tradeId: record.id, role: 'seller' }
      })
      
      console.log(`💰 Trade recorded: ${record.id} - ${trade.energyAmount} kWh @ ${trade.pricePerKwh}`)
      
      return record
    } catch (error) {
      console.error('Error recording trade:', error)
      throw error
    }
  }

  /**
   * Create audit trail entry
   */
  async createAuditEntry(entry: Omit<AuditTrail, 'id' | 'timestamp'>): Promise<AuditTrail> {
    try {
      const record: AuditTrail = {
        ...entry,
        id: this.generateAuditId(),
        timestamp: new Date()
      }

      await this.databaseService.createAuditEntry(record)
      
      console.log(`🔍 Audit entry: ${record.entityType}:${record.entityId} - ${record.action}`)
      
      return record
    } catch (error) {
      console.error('Error creating audit entry:', error)
      throw error
    }
  }

  /**
   * Get transaction history for a user
   */
  async getTransactionHistory(query: TransactionQuery): Promise<{ transactions: TransactionRecord[]; total: number }> {
    try {
      return await this.databaseService.getTransactionHistory(query)
    } catch (error) {
      console.error('Error fetching transaction history:', error)
      throw error
    }
  }

  /**
   * Get user activity history
   */
  async getUserActivity(
    userId: string, 
    options: { 
      activityType?: string
      startDate?: Date
      endDate?: Date
      limit?: number
      offset?: number 
    } = {}
  ): Promise<{ activities: UserActivity[]; total: number }> {
    try {
      return await this.databaseService.getUserActivity(userId, options)
    } catch (error) {
      console.error('Error fetching user activity:', error)
      throw error
    }
  }

  /**
   * Get trade history for a user
   */
  async getTradeHistory(
    userId: string,
    options: {
      role?: 'buyer' | 'seller' | 'both'
      status?: string
      startDate?: Date
      endDate?: Date
      limit?: number
      offset?: number
    } = {}
  ): Promise<{ trades: TradeHistory[]; total: number }> {
    try {
      return await this.databaseService.getTradeHistory(userId, options)
    } catch (error) {
      console.error('Error fetching trade history:', error)
      throw error
    }
  }

  /**
   * Get performance metrics for a user
   */
  async getPerformanceMetrics(userId: string, period: '24h' | '7d' | '30d' | '1y'): Promise<PerformanceMetrics> {
    try {
      return await this.databaseService.getPerformanceMetrics(userId, period)
    } catch (error) {
      console.error('Error fetching performance metrics:', error)
      throw error
    }
  }

  /**
   * Get audit trail for an entity
   */
  async getAuditTrail(
    entityType: string,
    entityId: string,
    options: {
      action?: string
      startDate?: Date
      endDate?: Date
      limit?: number
      offset?: number
    } = {}
  ): Promise<{ entries: AuditTrail[]; total: number }> {
    try {
      return await this.databaseService.getAuditTrail(entityType, entityId, options)
    } catch (error) {
      console.error('Error fetching audit trail:', error)
      throw error
    }
  }

  /**
   * Get transaction statistics
   */
  async getTransactionStats(period: '24h' | '7d' | '30d' = '24h'): Promise<{
    totalTransactions: number
    totalVolume: number
    totalFees: number
    averageTransactionSize: number
    successRate: number
    topTraders: Array<{ userId: string; volume: number; trades: number }>
  }> {
    try {
      return await this.databaseService.getTransactionStats(period)
    } catch (error) {
      console.error('Error fetching transaction stats:', error)
      throw error
    }
  }

  /**
   * Export user data (for GDPR compliance)
   */
  async exportUserData(userId: string): Promise<{
    transactions: TransactionRecord[]
    activities: UserActivity[]
    trades: TradeHistory[]
    auditEntries: AuditTrail[]
    metrics: PerformanceMetrics[]
  }> {
    try {
      const [transactions, activities, trades, auditEntries, metrics] = await Promise.all([
        this.databaseService.getAllUserTransactions(userId),
        this.databaseService.getAllUserActivities(userId),
        this.databaseService.getAllUserTrades(userId),
        this.databaseService.getAllUserAuditEntries(userId),
        this.databaseService.getAllUserMetrics(userId)
      ])

      return {
        transactions: transactions.transactions,
        activities: activities.activities,
        trades: trades.trades,
        auditEntries: auditEntries.entries,
        metrics
      }
    } catch (error) {
      console.error('Error exporting user data:', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async updatePendingTransactions(): Promise<void> {
    try {
      const pendingTransactions = await this.databaseService.getPendingTransactions()
      
      for (const transaction of pendingTransactions) {
        try {
          const status = await this.masChainService.getTransactionStatus(transaction.transactionHash)
          
          if (status.status !== 'pending') {
            await this.databaseService.updateTransactionStatus(
              transaction.id,
              status.status,
              status.blockNumber || 0
            )
            
            console.log(`🔄 Transaction updated: ${transaction.transactionHash} - ${status.status}`)
          }
        } catch (error) {
          console.error(`Error updating transaction ${transaction.transactionHash}:`, error)
        }
      }
    } catch (error) {
      console.error('Error updating pending transactions:', error)
    }
  }

  private async calculatePerformanceMetrics(): Promise<void> {
    try {
      const activeUsers = await this.databaseService.getActiveUsers()
      
      for (const userId of activeUsers) {
        for (const period of ['24h', '7d', '30d', '1y'] as const) {
          try {
            const metrics = await this.calculateUserMetrics(userId, period)
            await this.databaseService.savePerformanceMetrics(metrics)
          } catch (error) {
            console.error(`Error calculating metrics for user ${userId}, period ${period}:`, error)
          }
        }
      }
    } catch (error) {
      console.error('Error calculating performance metrics:', error)
    }
  }

  private async calculateUserMetrics(userId: string, period: '24h' | '7d' | '30d' | '1y'): Promise<PerformanceMetrics> {
    const trades = await this.databaseService.getUserTradesForPeriod(userId, period)
    
    const totalTrades = trades.length
    const totalVolume = trades.reduce((sum, trade) => sum + trade.energyAmount, 0)
    const totalRevenue = trades.filter(t => t.seller === userId).reduce((sum, trade) => sum + trade.totalPrice, 0)
    const totalCosts = trades.filter(t => t.buyer === userId).reduce((sum, trade) => sum + trade.totalPrice, 0)
    const netProfit = totalRevenue - totalCosts
    const averageTradeSize = totalTrades > 0 ? totalVolume / totalTrades : 0
    const successRate = totalTrades > 0 ? (trades.filter(t => t.status === 'completed').length / totalTrades) * 100 : 0
    const carbonOffset = totalVolume * 0.5 // Approximate carbon offset calculation
    const energyEfficiency = 85 // Placeholder calculation

    return {
      userId,
      period,
      totalTrades,
      totalVolume,
      totalRevenue,
      totalCosts,
      netProfit,
      averageTradeSize,
      successRate,
      carbonOffset,
      energyEfficiency,
      timestamp: new Date()
    }
  }

  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateActivityId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateAuditId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }
}
