import { WebSocketServer, WebSocket } from 'ws'
import { IncomingMessage } from 'http'
import jwt from 'jsonwebtoken'
import { AuthService } from './authService'

export interface WebSocketClient {
  id: string
  socket: WebSocket
  walletAddress?: string
  subscriptions: Set<string>
  lastPing: Date
  isAlive: boolean
}

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: Date
}

export interface MarketUpdateMessage {
  type: 'market_update'
  updateType: 'new_offer' | 'offer_cancelled' | 'trade_executed' | 'price_change'
  data: any
}

export interface EnergyUpdateMessage {
  type: 'energy_update'
  updateType: 'new_reading' | 'balance_change' | 'stats_update'
  data: any
}

export class WebSocketManager {
  private static instance: WebSocketManager
  private wss: WebSocketServer
  private clients: Map<string, WebSocketClient> = new Map()
  private authService: AuthService
  private heartbeatInterval: NodeJS.Timeout

  constructor(wss: WebSocketServer) {
    this.wss = wss
    this.authService = new AuthService()
    this.setupWebSocketServer()
    this.startHeartbeat()
  }

  static getInstance(wss?: WebSocketServer): WebSocketManager {
    if (!WebSocketManager.instance && wss) {
      WebSocketManager.instance = new WebSocketManager(wss)
    }
    return WebSocketManager.instance
  }

  /**
   * Setup WebSocket server with connection handling
   */
  private setupWebSocketServer(): void {
    this.wss.on('connection', async (socket: WebSocket, request: IncomingMessage) => {
      try {
        const clientId = this.generateClientId()
        const client: WebSocketClient = {
          id: clientId,
          socket,
          subscriptions: new Set(),
          lastPing: new Date(),
          isAlive: true
        }

        // Authenticate client if token provided
        const token = this.extractTokenFromRequest(request)
        if (token) {
          const walletAddress = await this.authenticateToken(token)
          if (walletAddress) {
            client.walletAddress = walletAddress
          }
        }

        this.clients.set(clientId, client)
        console.log(`🔌 WebSocket client connected: ${clientId}${client.walletAddress ? ` (${client.walletAddress})` : ''}`)

        // Setup message handlers
        socket.on('message', (data) => this.handleMessage(clientId, data))
        socket.on('close', () => this.handleDisconnection(clientId))
        socket.on('error', (error) => this.handleError(clientId, error))
        socket.on('pong', () => this.handlePong(clientId))

        // Send welcome message
        this.sendToClient(clientId, {
          type: 'connection_established',
          data: {
            clientId,
            authenticated: !!client.walletAddress,
            timestamp: new Date()
          }
        })

      } catch (error) {
        console.error('Error handling WebSocket connection:', error)
        socket.close(1011, 'Server error')
      }
    })
  }

  /**
   * Handle incoming messages from clients
   */
  private async handleMessage(clientId: string, data: Buffer): Promise<void> {
    try {
      const client = this.clients.get(clientId)
      if (!client) return

      const message = JSON.parse(data.toString())
      console.log(`📨 WebSocket message from ${clientId}:`, message.type)

      switch (message.type) {
        case 'authenticate':
          await this.handleAuthentication(clientId, message.data)
          break

        case 'subscribe':
          this.handleSubscription(clientId, message.data)
          break

        case 'unsubscribe':
          this.handleUnsubscription(clientId, message.data)
          break

        case 'ping':
          this.handlePing(clientId)
          break

        case 'get_market_data':
          await this.handleMarketDataRequest(clientId)
          break

        case 'get_energy_stats':
          await this.handleEnergyStatsRequest(clientId)
          break

        default:
          this.sendToClient(clientId, {
            type: 'error',
            data: { message: 'Unknown message type' }
          })
      }

    } catch (error) {
      console.error(`Error handling message from ${clientId}:`, error)
      this.sendToClient(clientId, {
        type: 'error',
        data: { message: 'Invalid message format' }
      })
    }
  }

  /**
   * Handle client authentication
   */
  private async handleAuthentication(clientId: string, authData: any): Promise<void> {
    try {
      const client = this.clients.get(clientId)
      if (!client) return

      const { token } = authData
      const walletAddress = await this.authenticateToken(token)

      if (walletAddress) {
        client.walletAddress = walletAddress
        this.sendToClient(clientId, {
          type: 'authentication_success',
          data: { walletAddress }
        })
        console.log(`✅ Client ${clientId} authenticated as ${walletAddress}`)
      } else {
        this.sendToClient(clientId, {
          type: 'authentication_failed',
          data: { message: 'Invalid token' }
        })
      }

    } catch (error) {
      console.error(`Authentication error for ${clientId}:`, error)
      this.sendToClient(clientId, {
        type: 'authentication_failed',
        data: { message: 'Authentication error' }
      })
    }
  }

  /**
   * Handle subscription requests
   */
  private handleSubscription(clientId: string, subscriptionData: any): void {
    const client = this.clients.get(clientId)
    if (!client) return

    const { channels } = subscriptionData
    if (Array.isArray(channels)) {
      channels.forEach(channel => {
        if (this.isValidChannel(channel)) {
          client.subscriptions.add(channel)
        }
      })
    }

    this.sendToClient(clientId, {
      type: 'subscription_confirmed',
      data: { 
        channels: Array.from(client.subscriptions),
        count: client.subscriptions.size
      }
    })
  }

  /**
   * Handle unsubscription requests
   */
  private handleUnsubscription(clientId: string, unsubscriptionData: any): void {
    const client = this.clients.get(clientId)
    if (!client) return

    const { channels } = unsubscriptionData
    if (Array.isArray(channels)) {
      channels.forEach(channel => client.subscriptions.delete(channel))
    }

    this.sendToClient(clientId, {
      type: 'unsubscription_confirmed',
      data: { 
        channels: Array.from(client.subscriptions),
        count: client.subscriptions.size
      }
    })
  }

  /**
   * Broadcast market updates to subscribed clients
   */
  broadcastMarketUpdate(updateType: string, data: any): void {
    const message: MarketUpdateMessage = {
      type: 'market_update',
      updateType: updateType as any,
      data
    }

    this.broadcastToSubscribers('market', message)
  }

  /**
   * Broadcast energy updates to subscribed clients
   */
  broadcastEnergyUpdate(updateType: string, data: any): void {
    const message: EnergyUpdateMessage = {
      type: 'energy_update',
      updateType: updateType as any,
      data
    }

    this.broadcastToSubscribers('energy', message)
  }

  /**
   * Send message to specific client
   */
  sendToClient(clientId: string, message: any): void {
    const client = this.clients.get(clientId)
    if (client && client.socket.readyState === WebSocket.OPEN) {
      const wsMessage: WebSocketMessage = {
        type: message.type,
        data: message.data,
        timestamp: new Date()
      }
      
      client.socket.send(JSON.stringify(wsMessage))
    }
  }

  /**
   * Send message to specific wallet address
   */
  sendToWallet(walletAddress: string, message: any): void {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.walletAddress === walletAddress) {
        this.sendToClient(clientId, message)
      }
    }
  }

  /**
   * Broadcast to all subscribers of a channel
   */
  private broadcastToSubscribers(channel: string, message: any): void {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.subscriptions.has(channel) && client.socket.readyState === WebSocket.OPEN) {
        this.sendToClient(clientId, message)
      }
    }
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(clientId: string): void {
    const client = this.clients.get(clientId)
    if (client) {
      console.log(`🔌 WebSocket client disconnected: ${clientId}${client.walletAddress ? ` (${client.walletAddress})` : ''}`)
      this.clients.delete(clientId)
    }
  }

  /**
   * Handle WebSocket errors
   */
  private handleError(clientId: string, error: Error): void {
    console.error(`WebSocket error for client ${clientId}:`, error)
    const client = this.clients.get(clientId)
    if (client) {
      client.socket.close(1011, 'Server error')
      this.clients.delete(clientId)
    }
  }

  /**
   * Handle ping/pong for connection health
   */
  private handlePing(clientId: string): void {
    const client = this.clients.get(clientId)
    if (client) {
      client.lastPing = new Date()
      client.socket.pong()
    }
  }

  private handlePong(clientId: string): void {
    const client = this.clients.get(clientId)
    if (client) {
      client.isAlive = true
    }
  }

  /**
   * Start heartbeat to check connection health
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      for (const [clientId, client] of this.clients.entries()) {
        if (!client.isAlive) {
          console.log(`💔 Terminating dead connection: ${clientId}`)
          client.socket.terminate()
          this.clients.delete(clientId)
          continue
        }

        client.isAlive = false
        client.socket.ping()
      }
    }, 30000) // Check every 30 seconds
  }

  /**
   * Helper methods
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private extractTokenFromRequest(request: IncomingMessage): string | null {
    const url = new URL(request.url || '', `http://${request.headers.host}`)
    return url.searchParams.get('token')
  }

  private async authenticateToken(token: string): Promise<string | null> {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any
      
      // Verify session is still valid
      const isValidSession = await this.authService.validateSession(decoded.sessionId)
      if (isValidSession) {
        return decoded.walletAddress
      }
      
      return null
    } catch (error) {
      return null
    }
  }

  private isValidChannel(channel: string): boolean {
    const validChannels = ['market', 'energy', 'trades', 'offers', 'stats']
    return validChannels.includes(channel)
  }

  private async handleMarketDataRequest(clientId: string): Promise<void> {
    // TODO: Implement market data request handling
    this.sendToClient(clientId, {
      type: 'market_data',
      data: { message: 'Market data request received' }
    })
  }

  private async handleEnergyStatsRequest(clientId: string): Promise<void> {
    // TODO: Implement energy stats request handling
    this.sendToClient(clientId, {
      type: 'energy_stats',
      data: { message: 'Energy stats request received' }
    })
  }

  /**
   * Get connection statistics
   */
  getStats(): {
    totalConnections: number
    authenticatedConnections: number
    subscriptions: { [channel: string]: number }
  } {
    const stats = {
      totalConnections: this.clients.size,
      authenticatedConnections: 0,
      subscriptions: {} as { [channel: string]: number }
    }

    for (const client of this.clients.values()) {
      if (client.walletAddress) {
        stats.authenticatedConnections++
      }

      for (const subscription of client.subscriptions) {
        stats.subscriptions[subscription] = (stats.subscriptions[subscription] || 0) + 1
      }
    }

    return stats
  }

  /**
   * Cleanup on shutdown
   */
  shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    for (const client of this.clients.values()) {
      client.socket.close(1001, 'Server shutting down')
    }

    this.clients.clear()
    console.log('🔌 WebSocket manager shut down')
  }
}
