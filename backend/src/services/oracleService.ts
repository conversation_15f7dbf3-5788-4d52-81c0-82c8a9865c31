import crypto from 'crypto'
import { MasChainService } from './masChainService'
import { DatabaseService } from './databaseService'
import { ValidationError, NotFoundError } from '../middleware/errorHandler'

export interface EnergyMeter {
  id: string
  owner: string
  meterId: string
  meterType: 'production' | 'consumption' | 'bidirectional'
  location: {
    latitude: number
    longitude: number
    address: string
  }
  specifications: {
    maxCapacity: number
    accuracy: number
    manufacturer?: string
    model?: string
  }
  publicKey: string
  isActive: boolean
  registeredAt: Date
  lastReading?: Date
}

export interface MeterReading {
  id: string
  meterId: string
  reading: {
    value: number
    timestamp: Date
    unit: 'kWh' | 'MWh' | 'GWh'
  }
  signature: string
  nonce: string
  verified: boolean
  validationResults: ValidationResult[]
  submittedAt: Date
  blockchainTxId?: string
}

export interface ValidationResult {
  validatorAddress: string
  isValid: boolean
  validatedAt: Date
  signature: string
  notes?: string
}

export interface RegisterMeterInput {
  owner: string
  meterId: string
  meterType: 'production' | 'consumption' | 'bidirectional'
  location: {
    latitude: number
    longitude: number
    address: string
  }
  specifications: {
    maxCapacity: number
    accuracy: number
    manufacturer?: string
    model?: string
  }
  publicKey: string
}

export interface SubmitReadingInput {
  meterId: string
  reading: {
    value: number
    timestamp: Date
    unit: 'kWh' | 'MWh' | 'GWh'
  }
  signature: string
  nonce: string
}

export interface VerifyReadingInput {
  readingId: string
  validatorSignature: string
  validatorAddress: string
  isValid: boolean
  validationNotes?: string
}

export interface ReadingQuery {
  limit: number
  offset: number
  startDate?: Date
  endDate?: Date
  verified?: boolean
}

export interface OracleStats {
  totalMeters: number
  activeMeters: number
  totalReadings: number
  verifiedReadings: number
  totalValidators: number
  averageValidationTime: number
}

export class OracleService {
  private masChainService: MasChainService
  private databaseService: DatabaseService
  private validationThreshold: number = 2 // Minimum validators required

  constructor() {
    this.masChainService = new MasChainService()
    this.databaseService = new DatabaseService()
  }

  /**
   * Register a new energy meter
   */
  async registerMeter(input: RegisterMeterInput): Promise<EnergyMeter> {
    try {
      // Validate input
      await this.validateMeterRegistration(input)

      // Check if meter ID is already registered
      const existingMeter = await this.databaseService.getMeterById(input.meterId)
      if (existingMeter) {
        throw new ValidationError('Meter ID already registered')
      }

      // Create meter record
      const meter: EnergyMeter = {
        id: this.generateMeterId(),
        owner: input.owner,
        meterId: input.meterId,
        meterType: input.meterType,
        location: input.location,
        specifications: input.specifications,
        publicKey: input.publicKey,
        isActive: true,
        registeredAt: new Date()
      }

      // Store in database
      await this.databaseService.createMeter(meter)

      // Register on blockchain (optional for additional security)
      try {
        const txId = await this.masChainService.registerMeterOnChain(meter)
        meter.blockchainTxId = txId
        await this.databaseService.updateMeter(meter.id, { blockchainTxId: txId })
      } catch (blockchainError) {
        console.warn('Failed to register meter on blockchain:', blockchainError)
        // Continue without blockchain registration for now
      }

      console.log(`✅ Meter registered: ${input.meterId} for owner ${input.owner}`)
      return meter
    } catch (error) {
      console.error('Error registering meter:', error)
      if (error instanceof ValidationError) {
        throw error
      }
      throw new Error('Failed to register meter')
    }
  }

  /**
   * Get user's registered meters
   */
  async getUserMeters(walletAddress: string): Promise<EnergyMeter[]> {
    try {
      return await this.databaseService.getMetersByOwner(walletAddress)
    } catch (error) {
      console.error('Error fetching user meters:', error)
      throw new Error('Failed to fetch user meters')
    }
  }

  /**
   * Submit a meter reading to the oracle
   */
  async submitReading(input: SubmitReadingInput): Promise<MeterReading> {
    try {
      // Validate reading
      await this.validateReading(input)

      // Get meter information
      const meter = await this.databaseService.getMeterById(input.meterId)
      if (!meter) {
        throw new NotFoundError('Meter not found')
      }

      if (!meter.isActive) {
        throw new ValidationError('Meter is not active')
      }

      // Verify signature
      const isValidSignature = await this.verifyReadingSignature(input, meter)
      if (!isValidSignature) {
        throw new ValidationError('Invalid reading signature')
      }

      // Check for duplicate nonce
      const isDuplicateNonce = await this.databaseService.checkNonceUsed(input.nonce, input.meterId)
      if (isDuplicateNonce) {
        throw new ValidationError('Nonce already used')
      }

      // Create reading record
      const reading: MeterReading = {
        id: this.generateReadingId(),
        meterId: input.meterId,
        reading: input.reading,
        signature: input.signature,
        nonce: input.nonce,
        verified: false,
        validationResults: [],
        submittedAt: new Date()
      }

      // Store in database
      await this.databaseService.createMeterReading(reading)

      // Mark nonce as used
      await this.databaseService.markNonceAsUsed(input.nonce, input.meterId)

      // Trigger validation process
      await this.initiateValidation(reading.id)

      console.log(`📊 Reading submitted: ${reading.id} for meter ${input.meterId}`)
      return reading
    } catch (error) {
      console.error('Error submitting reading:', error)
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error
      }
      throw new Error('Failed to submit reading')
    }
  }

  /**
   * Get readings for a specific meter
   */
  async getMeterReadings(
    meterId: string,
    ownerAddress: string,
    query: ReadingQuery
  ): Promise<{ readings: MeterReading[]; total: number }> {
    try {
      // Verify meter ownership
      const meter = await this.databaseService.getMeterById(meterId)
      if (!meter || meter.owner !== ownerAddress) {
        throw new ValidationError('Meter not found or access denied')
      }

      return await this.databaseService.getMeterReadings(meterId, query)
    } catch (error) {
      console.error('Error fetching meter readings:', error)
      if (error instanceof ValidationError) {
        throw error
      }
      throw new Error('Failed to fetch meter readings')
    }
  }

  /**
   * Verify a submitted reading (validator endpoint)
   */
  async verifyReading(input: VerifyReadingInput): Promise<ValidationResult> {
    try {
      // Get the reading
      const reading = await this.databaseService.getMeterReadingById(input.readingId)
      if (!reading) {
        throw new NotFoundError('Reading not found')
      }

      // Verify validator signature
      const validationMessage = this.createValidationMessage(input.readingId, input.isValid)
      const isValidValidatorSignature = await this.masChainService.verifySignature(
        input.validatorAddress,
        validationMessage,
        input.validatorSignature
      )

      if (!isValidValidatorSignature) {
        throw new ValidationError('Invalid validator signature')
      }

      // Check if validator is authorized
      const isAuthorizedValidator = await this.databaseService.isAuthorizedValidator(input.validatorAddress)
      if (!isAuthorizedValidator) {
        throw new ValidationError('Unauthorized validator')
      }

      // Create validation result
      const validationResult: ValidationResult = {
        validatorAddress: input.validatorAddress,
        isValid: input.isValid,
        validatedAt: new Date(),
        signature: input.validatorSignature,
        notes: input.validationNotes
      }

      // Store validation result
      await this.databaseService.addValidationResult(input.readingId, validationResult)

      // Check if reading is now fully validated
      await this.checkReadingValidation(input.readingId)

      console.log(`✅ Reading validated: ${input.readingId} by ${input.validatorAddress}`)
      return validationResult
    } catch (error) {
      console.error('Error verifying reading:', error)
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error
      }
      throw new Error('Failed to verify reading')
    }
  }

  /**
   * Get validation status for a reading
   */
  async getValidationStatus(readingId: string): Promise<{
    reading: MeterReading
    validationCount: number
    requiredValidations: number
    isFullyValidated: boolean
  }> {
    try {
      const reading = await this.databaseService.getMeterReadingById(readingId)
      if (!reading) {
        throw new NotFoundError('Reading not found')
      }

      const validValidations = reading.validationResults.filter(v => v.isValid).length
      const isFullyValidated = validValidations >= this.validationThreshold

      return {
        reading,
        validationCount: validValidations,
        requiredValidations: this.validationThreshold,
        isFullyValidated
      }
    } catch (error) {
      console.error('Error fetching validation status:', error)
      if (error instanceof NotFoundError) {
        throw error
      }
      throw new Error('Failed to fetch validation status')
    }
  }

  /**
   * Get oracle network statistics
   */
  async getOracleStats(): Promise<OracleStats> {
    try {
      return await this.databaseService.getOracleStats()
    } catch (error) {
      console.error('Error fetching oracle stats:', error)
      throw new Error('Failed to fetch oracle statistics')
    }
  }

  /**
   * Private helper methods
   */
  private async validateMeterRegistration(input: RegisterMeterInput): Promise<void> {
    if (!input.meterId || input.meterId.length < 3) {
      throw new ValidationError('Meter ID must be at least 3 characters')
    }

    if (input.specifications.maxCapacity <= 0) {
      throw new ValidationError('Max capacity must be positive')
    }

    if (input.specifications.accuracy < 0 || input.specifications.accuracy > 100) {
      throw new ValidationError('Accuracy must be between 0 and 100')
    }

    if (Math.abs(input.location.latitude) > 90) {
      throw new ValidationError('Invalid latitude')
    }

    if (Math.abs(input.location.longitude) > 180) {
      throw new ValidationError('Invalid longitude')
    }
  }

  private async validateReading(input: SubmitReadingInput): Promise<void> {
    if (input.reading.value < 0) {
      throw new ValidationError('Reading value cannot be negative')
    }

    // Check timestamp is not too old or in future
    const now = new Date()
    const timeDiff = Math.abs(now.getTime() - input.reading.timestamp.getTime())
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours

    if (timeDiff > maxAge) {
      throw new ValidationError('Reading timestamp is too old or in future')
    }

    if (!['kWh', 'MWh', 'GWh'].includes(input.reading.unit)) {
      throw new ValidationError('Invalid energy unit')
    }
  }

  private async verifyReadingSignature(input: SubmitReadingInput, meter: EnergyMeter): Promise<boolean> {
    const message = this.createReadingMessage(input)
    
    // For now, we'll use a simplified verification
    // In production, this would verify against the meter's public key
    return input.signature.length > 10 // Placeholder verification
  }

  private createReadingMessage(input: SubmitReadingInput): string {
    return `${input.meterId}:${input.reading.value}:${input.reading.unit}:${input.reading.timestamp.toISOString()}:${input.nonce}`
  }

  private createValidationMessage(readingId: string, isValid: boolean): string {
    return `validate:${readingId}:${isValid}:${Date.now()}`
  }

  private async initiateValidation(readingId: string): Promise<void> {
    // In a real implementation, this would notify validators
    // For now, we'll just log the initiation
    console.log(`🔍 Validation initiated for reading: ${readingId}`)
  }

  private async checkReadingValidation(readingId: string): Promise<void> {
    const status = await this.getValidationStatus(readingId)
    
    if (status.isFullyValidated && !status.reading.verified) {
      // Mark reading as verified
      await this.databaseService.markReadingAsVerified(readingId)
      
      // Submit to blockchain if needed
      try {
        const txId = await this.masChainService.submitVerifiedReading(status.reading)
        await this.databaseService.updateMeterReading(readingId, { blockchainTxId: txId })
      } catch (blockchainError) {
        console.warn('Failed to submit verified reading to blockchain:', blockchainError)
      }

      console.log(`✅ Reading fully validated and verified: ${readingId}`)
    }
  }

  private generateMeterId(): string {
    return `meter_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateReadingId(): string {
    return `reading_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }
}
