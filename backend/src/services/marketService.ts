import { MasChainService } from './masChainService'
import { DatabaseService } from './databaseService'
import { WebSocketManager } from './websocket'
import { getCacheService } from './cacheService'
import { ValidationError, NotFoundError, ConflictError, BlockchainError } from '../middleware/errorHandler'

export interface EnergyOffer {
  id: string
  seller: string
  energyAmount: number
  pricePerKwh: number
  offerType: 'immediate' | 'scheduled' | 'recurring'
  status: 'active' | 'completed' | 'cancelled' | 'expired'
  createdAt: Date
  expiresAt: Date
  filledAmount: number
  scheduledFor?: Date
  recurringPattern?: RecurringPattern
  blockchainTxId?: string
}

export interface RecurringPattern {
  frequency: 'daily' | 'weekly' | 'monthly'
  daysOfWeek?: number[] // 0-6, Sunday = 0
  timeOfDay?: string // HH:MM format
  duration: number // hours
}

export interface CreateOfferInput {
  seller: string
  energyAmount: number
  pricePerKwh: number
  offerType: 'immediate' | 'scheduled' | 'recurring'
  duration: number // hours
  scheduledFor?: Date
  recurringPattern?: RecurringPattern
}

export interface OfferQuery {
  limit: number
  offset: number
  type?: 'immediate' | 'scheduled' | 'recurring'
  priceRange?: { min?: number; max?: number }
  amountRange?: { min?: number; max?: number }
}

export interface UserOfferQuery {
  status?: 'active' | 'completed' | 'cancelled' | 'expired'
  limit: number
  offset: number
}

export interface ExecuteTradeInput {
  buyer: string
  offerId: string
  amount: number
  maxPrice?: number
}

export interface Trade {
  id: string
  buyer: string
  seller: string
  offerId: string
  amount: number
  pricePerKwh: number
  totalPrice: number
  status: 'pending' | 'completed' | 'failed'
  executedAt: Date
  blockchainTxId?: string
}

export interface MarketStats {
  totalOffers: number
  totalVolumeTraded: number
  averagePrice: number
  activeOffers: number
  completedTrades: number
  totalParticipants: number
}

export interface PriceHistoryQuery {
  period: '1h' | '24h' | '7d' | '30d'
  interval: '1m' | '5m' | '15m' | '1h' | '1d'
}

export interface PricePoint {
  timestamp: Date
  price: number
  volume: number
}

export class MarketService {
  private masChainService: MasChainService
  private databaseService: DatabaseService
  private wsManager: WebSocketManager
  private cacheService = getCacheService()

  constructor() {
    this.masChainService = new MasChainService()
    this.databaseService = new DatabaseService()
    this.wsManager = WebSocketManager.getInstance()
  }

  /**
   * Get active market offers
   */
  async getActiveOffers(query: OfferQuery): Promise<{ offers: EnergyOffer[]; total: number }> {
    try {
      // Create cache key based on query parameters
      const cacheKey = `offers:active:${JSON.stringify(query)}`

      // Try to get from cache first
      const cached = await this.cacheService.get<{ offers: EnergyOffer[]; total: number }>(cacheKey)
      if (cached) {
        return cached
      }

      const result = await this.databaseService.getActiveOffers(query)

      // Update expired offers
      await this.updateExpiredOffers()

      // Cache the result for 60 seconds
      await this.cacheService.set(cacheKey, result, { ttl: 60 })

      return result
    } catch (error) {
      console.error('Error fetching active offers:', error)
      throw new Error('Failed to fetch market offers')
    }
  }

  /**
   * Create a new energy offer
   */
  async createOffer(input: CreateOfferInput): Promise<EnergyOffer> {
    try {
      // Validate offer input
      await this.validateOfferInput(input)

      // Check user has sufficient energy balance
      const userBalance = await this.masChainService.getEnergyBalance(input.seller)
      if (userBalance < input.energyAmount) {
        throw new ValidationError('Insufficient energy balance to create offer')
      }

      // Calculate expiration time
      const expiresAt = new Date(Date.now() + (input.duration * 60 * 60 * 1000))

      // Create offer
      const offer: EnergyOffer = {
        id: this.generateOfferId(),
        seller: input.seller,
        energyAmount: input.energyAmount,
        pricePerKwh: input.pricePerKwh,
        offerType: input.offerType,
        status: 'active',
        createdAt: new Date(),
        expiresAt,
        filledAmount: 0,
        scheduledFor: input.scheduledFor,
        recurringPattern: input.recurringPattern
      }

      // Store in database
      const createdOffer = await this.databaseService.createOffer(offer)

      // Lock energy credits on blockchain
      const lockTxId = await this.masChainService.lockEnergyCredits(
        input.seller,
        input.energyAmount,
        createdOffer.id
      )

      // Update offer with blockchain transaction ID
      createdOffer.blockchainTxId = lockTxId
      await this.databaseService.updateOffer(createdOffer.id, { blockchainTxId: lockTxId })

      // Broadcast new offer to WebSocket clients
      this.wsManager.broadcastMarketUpdate('new_offer', createdOffer)

      return createdOffer
    } catch (error) {
      console.error('Error creating offer:', error)
      if (error instanceof ValidationError) {
        throw error
      }
      throw new BlockchainError('Failed to create offer')
    }
  }

  /**
   * Get user's offers
   */
  async getUserOffers(
    walletAddress: string, 
    query: UserOfferQuery
  ): Promise<{ offers: EnergyOffer[]; total: number }> {
    try {
      return await this.databaseService.getUserOffers(walletAddress, query)
    } catch (error) {
      console.error('Error fetching user offers:', error)
      throw new Error('Failed to fetch user offers')
    }
  }

  /**
   * Execute a trade
   */
  async executeTrade(input: ExecuteTradeInput): Promise<Trade> {
    try {
      // Get the offer
      const offer = await this.databaseService.getOfferById(input.offerId)
      if (!offer) {
        throw new NotFoundError('Offer not found')
      }

      // Validate trade
      await this.validateTrade(input, offer)

      // Check buyer has sufficient balance
      const totalPrice = input.amount * offer.pricePerKwh
      const buyerBalance = await this.masChainService.getTokenBalance(input.buyer)
      
      if (buyerBalance < totalPrice) {
        throw new ValidationError('Insufficient balance to execute trade')
      }

      // Create trade record
      const trade: Trade = {
        id: this.generateTradeId(),
        buyer: input.buyer,
        seller: offer.seller,
        offerId: input.offerId,
        amount: input.amount,
        pricePerKwh: offer.pricePerKwh,
        totalPrice,
        status: 'pending',
        executedAt: new Date()
      }

      // Store trade in database
      const createdTrade = await this.databaseService.createTrade(trade)

      // Execute trade on blockchain
      const tradeTxId = await this.masChainService.executeTrade({
        buyer: input.buyer,
        seller: offer.seller,
        energyAmount: input.amount,
        pricePerKwh: offer.pricePerKwh,
        offerId: input.offerId
      })

      // Update trade with blockchain transaction ID
      createdTrade.blockchainTxId = tradeTxId
      createdTrade.status = 'completed'
      await this.databaseService.updateTrade(createdTrade.id, {
        blockchainTxId: tradeTxId,
        status: 'completed'
      })

      // Update offer filled amount
      const newFilledAmount = offer.filledAmount + input.amount
      await this.databaseService.updateOffer(input.offerId, {
        filledAmount: newFilledAmount,
        status: newFilledAmount >= offer.energyAmount ? 'completed' : 'active'
      })

      // Broadcast trade to WebSocket clients
      this.wsManager.broadcastMarketUpdate('trade_executed', createdTrade)

      return createdTrade
    } catch (error) {
      console.error('Error executing trade:', error)
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error
      }
      throw new BlockchainError('Failed to execute trade')
    }
  }

  /**
   * Cancel an offer
   */
  async cancelOffer(offerId: string, walletAddress: string): Promise<void> {
    try {
      const offer = await this.databaseService.getOfferById(offerId)
      if (!offer) {
        throw new NotFoundError('Offer not found')
      }

      if (offer.seller !== walletAddress) {
        throw new ValidationError('Only offer creator can cancel the offer')
      }

      if (offer.status !== 'active') {
        throw new ValidationError('Only active offers can be cancelled')
      }

      // Unlock energy credits on blockchain
      if (offer.blockchainTxId) {
        await this.masChainService.unlockEnergyCredits(
          offer.seller,
          offer.energyAmount - offer.filledAmount,
          offer.id
        )
      }

      // Update offer status
      await this.databaseService.updateOffer(offerId, { status: 'cancelled' })

      // Broadcast cancellation
      this.wsManager.broadcastMarketUpdate('offer_cancelled', { offerId })
    } catch (error) {
      console.error('Error cancelling offer:', error)
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error
      }
      throw new BlockchainError('Failed to cancel offer')
    }
  }

  /**
   * Get market statistics
   */
  async getMarketStats(): Promise<MarketStats> {
    try {
      return await this.databaseService.getMarketStats()
    } catch (error) {
      console.error('Error fetching market stats:', error)
      throw new Error('Failed to fetch market statistics')
    }
  }

  /**
   * Get price history
   */
  async getPriceHistory(query: PriceHistoryQuery): Promise<PricePoint[]> {
    try {
      return await this.databaseService.getPriceHistory(query)
    } catch (error) {
      console.error('Error fetching price history:', error)
      throw new Error('Failed to fetch price history')
    }
  }

  /**
   * Private helper methods
   */
  private async validateOfferInput(input: CreateOfferInput): Promise<void> {
    if (input.energyAmount <= 0) {
      throw new ValidationError('Energy amount must be positive')
    }

    if (input.pricePerKwh <= 0) {
      throw new ValidationError('Price per kWh must be positive')
    }

    if (input.duration <= 0 || input.duration > 8760) {
      throw new ValidationError('Duration must be between 1 and 8760 hours')
    }

    if (input.offerType === 'scheduled' && !input.scheduledFor) {
      throw new ValidationError('Scheduled offers must have a scheduled time')
    }

    if (input.offerType === 'recurring' && !input.recurringPattern) {
      throw new ValidationError('Recurring offers must have a recurring pattern')
    }
  }

  private async validateTrade(input: ExecuteTradeInput, offer: EnergyOffer): Promise<void> {
    if (offer.status !== 'active') {
      throw new ValidationError('Offer is not active')
    }

    if (offer.seller === input.buyer) {
      throw new ValidationError('Cannot buy from yourself')
    }

    if (input.amount <= 0) {
      throw new ValidationError('Trade amount must be positive')
    }

    const availableAmount = offer.energyAmount - offer.filledAmount
    if (input.amount > availableAmount) {
      throw new ValidationError('Requested amount exceeds available amount')
    }

    if (input.maxPrice && offer.pricePerKwh > input.maxPrice) {
      throw new ValidationError('Offer price exceeds maximum price')
    }

    if (new Date() > offer.expiresAt) {
      throw new ValidationError('Offer has expired')
    }
  }

  private async updateExpiredOffers(): Promise<void> {
    try {
      await this.databaseService.updateExpiredOffers()
    } catch (error) {
      console.error('Error updating expired offers:', error)
    }
  }

  private generateOfferId(): string {
    return `offer_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }
}
