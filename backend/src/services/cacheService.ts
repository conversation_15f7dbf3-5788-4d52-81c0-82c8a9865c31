import Redis from 'ioredis'

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  prefix?: string
  compress?: boolean
}

export class CacheService {
  private redis: Redis
  private defaultTTL: number = 300 // 5 minutes default
  private keyPrefix: string = 'maschain:energy:'

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
      keyPrefix: this.keyPrefix
    })

    this.redis.on('error', (error) => {
      console.error('Redis connection error:', error)
    })

    this.redis.on('connect', () => {
      console.log('✅ Redis connected successfully')
    })
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key)
      if (!value) return null
      
      return JSON.parse(value) as T
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set value in cache
   */
  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      const ttl = options.ttl || this.defaultTTL
      const serializedValue = JSON.stringify(value)
      
      if (ttl > 0) {
        await this.redis.setex(key, ttl, serializedValue)
      } else {
        await this.redis.set(key, serializedValue)
      }
      
      return true
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Delete key from cache
   */
  async del(key: string): Promise<boolean> {
    try {
      const result = await this.redis.del(key)
      return result > 0
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Delete multiple keys matching pattern
   */
  async delPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.redis.keys(pattern)
      if (keys.length === 0) return 0
      
      const result = await this.redis.del(...keys)
      return result
    } catch (error) {
      console.error(`Cache delete pattern error for pattern ${pattern}:`, error)
      return 0
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key)
      return result === 1
    } catch (error) {
      console.error(`Cache exists error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Set expiration for existing key
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const result = await this.redis.expire(key, ttl)
      return result === 1
    } catch (error) {
      console.error(`Cache expire error for key ${key}:`, error)
      return false
    }
  }

  /**
   * Get or set pattern - if key doesn't exist, execute function and cache result
   */
  async getOrSet<T>(
    key: string, 
    fetchFunction: () => Promise<T>, 
    options: CacheOptions = {}
  ): Promise<T> {
    try {
      // Try to get from cache first
      const cached = await this.get<T>(key)
      if (cached !== null) {
        return cached
      }

      // If not in cache, execute function
      const result = await fetchFunction()
      
      // Cache the result
      await this.set(key, result, options)
      
      return result
    } catch (error) {
      console.error(`Cache getOrSet error for key ${key}:`, error)
      // If cache fails, still return the function result
      return await fetchFunction()
    }
  }

  /**
   * Increment counter
   */
  async incr(key: string, ttl?: number): Promise<number> {
    try {
      const result = await this.redis.incr(key)
      if (ttl && result === 1) {
        await this.redis.expire(key, ttl)
      }
      return result
    } catch (error) {
      console.error(`Cache incr error for key ${key}:`, error)
      return 0
    }
  }

  /**
   * Add to set
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.redis.sadd(key, ...members)
    } catch (error) {
      console.error(`Cache sadd error for key ${key}:`, error)
      return 0
    }
  }

  /**
   * Get set members
   */
  async smembers(key: string): Promise<string[]> {
    try {
      return await this.redis.smembers(key)
    } catch (error) {
      console.error(`Cache smembers error for key ${key}:`, error)
      return []
    }
  }

  /**
   * Remove from set
   */
  async srem(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.redis.srem(key, ...members)
    } catch (error) {
      console.error(`Cache srem error for key ${key}:`, error)
      return 0
    }
  }

  /**
   * Push to list
   */
  async lpush(key: string, ...values: string[]): Promise<number> {
    try {
      return await this.redis.lpush(key, ...values)
    } catch (error) {
      console.error(`Cache lpush error for key ${key}:`, error)
      return 0
    }
  }

  /**
   * Get list range
   */
  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    try {
      return await this.redis.lrange(key, start, stop)
    } catch (error) {
      console.error(`Cache lrange error for key ${key}:`, error)
      return []
    }
  }

  /**
   * Set hash field
   */
  async hset(key: string, field: string, value: string): Promise<number> {
    try {
      return await this.redis.hset(key, field, value)
    } catch (error) {
      console.error(`Cache hset error for key ${key}:`, error)
      return 0
    }
  }

  /**
   * Get hash field
   */
  async hget(key: string, field: string): Promise<string | null> {
    try {
      return await this.redis.hget(key, field)
    } catch (error) {
      console.error(`Cache hget error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Get all hash fields
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    try {
      return await this.redis.hgetall(key)
    } catch (error) {
      console.error(`Cache hgetall error for key ${key}:`, error)
      return {}
    }
  }

  /**
   * Cache market data with specific TTL
   */
  async cacheMarketData(data: any): Promise<void> {
    await this.set('market:stats', data, { ttl: 60 }) // 1 minute TTL
  }

  /**
   * Cache user offers with specific TTL
   */
  async cacheUserOffers(walletAddress: string, offers: any[]): Promise<void> {
    await this.set(`user:${walletAddress}:offers`, offers, { ttl: 120 }) // 2 minutes TTL
  }

  /**
   * Cache order book data
   */
  async cacheOrderBook(orderBook: any): Promise<void> {
    await this.set('orderbook:snapshot', orderBook, { ttl: 30 }) // 30 seconds TTL
  }

  /**
   * Cache energy readings
   */
  async cacheEnergyReadings(walletAddress: string, readings: any[]): Promise<void> {
    await this.set(`energy:${walletAddress}:readings`, readings, { ttl: 300 }) // 5 minutes TTL
  }

  /**
   * Cache analytics data
   */
  async cacheAnalytics(walletAddress: string, period: string, data: any): Promise<void> {
    await this.set(`analytics:${walletAddress}:${period}`, data, { ttl: 600 }) // 10 minutes TTL
  }

  /**
   * Invalidate user-related caches
   */
  async invalidateUserCache(walletAddress: string): Promise<void> {
    await this.delPattern(`user:${walletAddress}:*`)
    await this.delPattern(`energy:${walletAddress}:*`)
    await this.delPattern(`analytics:${walletAddress}:*`)
  }

  /**
   * Invalidate market-related caches
   */
  async invalidateMarketCache(): Promise<void> {
    await this.delPattern('market:*')
    await this.delPattern('orderbook:*')
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    try {
      const info = await this.redis.info('memory')
      const keyspace = await this.redis.info('keyspace')
      
      return {
        memory: info,
        keyspace: keyspace,
        connected: this.redis.status === 'ready'
      }
    } catch (error) {
      console.error('Cache stats error:', error)
      return { connected: false }
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redis.ping()
      return result === 'PONG'
    } catch (error) {
      console.error('Cache health check error:', error)
      return false
    }
  }

  /**
   * Close connection
   */
  async close(): Promise<void> {
    await this.redis.quit()
  }
}

// Singleton instance
let cacheServiceInstance: CacheService | null = null

export function getCacheService(): CacheService {
  if (!cacheServiceInstance) {
    cacheServiceInstance = new CacheService()
  }
  return cacheServiceInstance
}
