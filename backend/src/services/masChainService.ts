import axios, { AxiosInstance } from 'axios'
import crypto from 'crypto'
import { BlockchainError, ValidationError } from '../middleware/errorHandler'

export interface MasChainConfig {
  apiUrl: string
  apiKey: string
  apiSecret: string
  projectId: string
  environment: 'testnet' | 'mainnet'
}

export interface WalletCreateResponse {
  walletId: string
  walletAddress: string
  publicKey: string
}

export interface TokenCreateResponse {
  tokenId: string
  contractAddress: string
  transactionId: string
}

export interface TransactionResponse {
  transactionId: string
  status: 'pending' | 'confirmed' | 'failed'
  blockNumber?: number
  gasUsed?: number
}

export interface TradeExecutionInput {
  buyer: string
  seller: string
  energyAmount: number
  pricePerKwh: number
  offerId: string
}

export class MasChainService {
  private config: MasChainConfig
  private apiClient: AxiosInstance
  private energyCreditTokenId: string | null = null

  constructor() {
    this.config = this.loadConfig()
    this.apiClient = this.createApiClient()
  }

  /**
   * Initialize the service and deploy contracts if needed
   */
  async initialize(): Promise<void> {
    try {
      // Check if energy credit token exists
      this.energyCreditTokenId = await this.getOrCreateEnergyToken()
      console.log('✅ MasChain service initialized with token:', this.energyCreditTokenId)
    } catch (error) {
      console.error('❌ Failed to initialize MasChain service:', error)
      throw new BlockchainError('Failed to initialize blockchain service')
    }
  }

  /**
   * Create a new wallet
   */
  async createWallet(userId: string): Promise<WalletCreateResponse> {
    try {
      const response = await this.apiClient.post('/api/v1/wallet/create', {
        userId,
        walletName: `Energy Trading Wallet - ${userId}`,
        description: 'Wallet for energy trading platform'
      })

      return {
        walletId: response.data.walletId,
        walletAddress: response.data.walletAddress,
        publicKey: response.data.publicKey
      }
    } catch (error) {
      console.error('Error creating wallet:', error)
      throw new BlockchainError('Failed to create wallet')
    }
  }

  /**
   * Get wallet balance (native token)
   */
  async getWalletBalance(walletAddress: string): Promise<number> {
    try {
      const response = await this.apiClient.post('/api/v1/wallet/balance', {
        walletAddress
      })

      return parseFloat(response.data.balance || '0')
    } catch (error) {
      console.error('Error fetching wallet balance:', error)
      throw new BlockchainError('Failed to fetch wallet balance')
    }
  }

  /**
   * Get energy credit token balance
   */
  async getEnergyBalance(walletAddress: string): Promise<number> {
    try {
      if (!this.energyCreditTokenId) {
        await this.initialize()
      }

      const response = await this.apiClient.post('/api/v1/token/balance', {
        walletAddress,
        tokenId: this.energyCreditTokenId
      })

      return parseFloat(response.data.balance || '0')
    } catch (error) {
      console.error('Error fetching energy balance:', error)
      throw new BlockchainError('Failed to fetch energy balance')
    }
  }

  /**
   * Get general token balance
   */
  async getTokenBalance(walletAddress: string, tokenId?: string): Promise<number> {
    try {
      const targetTokenId = tokenId || this.energyCreditTokenId
      if (!targetTokenId) {
        throw new ValidationError('Token ID not specified')
      }

      const response = await this.apiClient.post('/api/v1/token/balance', {
        walletAddress,
        tokenId: targetTokenId
      })

      return parseFloat(response.data.balance || '0')
    } catch (error) {
      console.error('Error fetching token balance:', error)
      throw new BlockchainError('Failed to fetch token balance')
    }
  }

  /**
   * Mint energy credits
   */
  async mintEnergyCredits(walletAddress: string, amount: number): Promise<string> {
    try {
      if (!this.energyCreditTokenId) {
        await this.initialize()
      }

      const response = await this.apiClient.post('/api/v1/token/mint', {
        tokenId: this.energyCreditTokenId,
        walletAddress,
        amount: amount.toString(),
        description: `Minted ${amount} EC from verified energy production`
      })

      return response.data.transactionId
    } catch (error) {
      console.error('Error minting energy credits:', error)
      throw new BlockchainError('Failed to mint energy credits')
    }
  }

  /**
   * Lock energy credits for an offer
   */
  async lockEnergyCredits(walletAddress: string, amount: number, offerId: string): Promise<string> {
    try {
      // For now, we'll use a transfer to a escrow address
      // In a full implementation, this would use a smart contract
      const escrowAddress = await this.getEscrowAddress()
      
      const response = await this.apiClient.post('/api/v1/token/transfer', {
        tokenId: this.energyCreditTokenId,
        fromWalletAddress: walletAddress,
        toWalletAddress: escrowAddress,
        amount: amount.toString(),
        description: `Locked ${amount} EC for offer ${offerId}`
      })

      return response.data.transactionId
    } catch (error) {
      console.error('Error locking energy credits:', error)
      throw new BlockchainError('Failed to lock energy credits')
    }
  }

  /**
   * Unlock energy credits (cancel offer)
   */
  async unlockEnergyCredits(walletAddress: string, amount: number, offerId: string): Promise<string> {
    try {
      const escrowAddress = await this.getEscrowAddress()
      
      const response = await this.apiClient.post('/api/v1/token/transfer', {
        tokenId: this.energyCreditTokenId,
        fromWalletAddress: escrowAddress,
        toWalletAddress: walletAddress,
        amount: amount.toString(),
        description: `Unlocked ${amount} EC from cancelled offer ${offerId}`
      })

      return response.data.transactionId
    } catch (error) {
      console.error('Error unlocking energy credits:', error)
      throw new BlockchainError('Failed to unlock energy credits')
    }
  }

  /**
   * Execute a trade between buyer and seller
   */
  async executeTrade(input: TradeExecutionInput): Promise<string> {
    try {
      const escrowAddress = await this.getEscrowAddress()
      const totalPrice = input.energyAmount * input.pricePerKwh

      // Transfer energy credits from escrow to buyer
      const energyTransferResponse = await this.apiClient.post('/api/v1/token/transfer', {
        tokenId: this.energyCreditTokenId,
        fromWalletAddress: escrowAddress,
        toWalletAddress: input.buyer,
        amount: input.energyAmount.toString(),
        description: `Energy transfer for trade ${input.offerId}`
      })

      // Transfer payment from buyer to seller (assuming payment token exists)
      // For now, we'll use native token
      const paymentResponse = await this.apiClient.post('/api/v1/wallet/transfer', {
        fromWalletAddress: input.buyer,
        toWalletAddress: input.seller,
        amount: totalPrice.toString(),
        description: `Payment for ${input.energyAmount} EC at ${input.pricePerKwh}/kWh`
      })

      return energyTransferResponse.data.transactionId
    } catch (error) {
      console.error('Error executing trade:', error)
      throw new BlockchainError('Failed to execute trade')
    }
  }

  /**
   * Verify wallet signature
   */
  async verifySignature(walletAddress: string, message: string, signature: string): Promise<boolean> {
    try {
      const response = await this.apiClient.post('/api/v1/wallet/verify-signature', {
        walletAddress,
        message,
        signature
      })

      return response.data.isValid === true
    } catch (error) {
      console.error('Error verifying signature:', error)
      return false
    }
  }

  /**
   * Get transaction status
   */
  async getTransactionStatus(transactionId: string): Promise<TransactionResponse> {
    try {
      const response = await this.apiClient.get(`/api/v1/transaction/${transactionId}`)
      
      return {
        transactionId,
        status: response.data.status,
        blockNumber: response.data.blockNumber,
        gasUsed: response.data.gasUsed
      }
    } catch (error) {
      console.error('Error fetching transaction status:', error)
      throw new BlockchainError('Failed to fetch transaction status')
    }
  }

  /**
   * Private helper methods
   */
  private loadConfig(): MasChainConfig {
    return {
      apiUrl: process.env.MASCHAIN_API_URL || 'https://service-testnet.maschain.com',
      apiKey: process.env.MASCHAIN_API_KEY || '',
      apiSecret: process.env.MASCHAIN_API_SECRET || '',
      projectId: process.env.MASCHAIN_PROJECT_ID || '',
      environment: (process.env.MASCHAIN_ENVIRONMENT as 'testnet' | 'mainnet') || 'testnet'
    }
  }

  private createApiClient(): AxiosInstance {
    const client = axios.create({
      baseURL: this.config.apiUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        'X-API-Secret': this.config.apiSecret,
        'X-Project-ID': this.config.projectId
      }
    })

    // Add request interceptor for logging
    client.interceptors.request.use(
      (config) => {
        console.log(`🔗 MasChain API Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('🔗 MasChain API Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Add response interceptor for error handling
    client.interceptors.response.use(
      (response) => {
        console.log(`✅ MasChain API Response: ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error('❌ MasChain API Error:', error.response?.data || error.message)
        return Promise.reject(error)
      }
    )

    return client
  }

  private async getOrCreateEnergyToken(): Promise<string> {
    try {
      // Try to get existing token first
      const existingTokenId = process.env.ENERGY_CREDIT_TOKEN_ID
      if (existingTokenId) {
        return existingTokenId
      }

      // Create new energy credit token
      const response = await this.apiClient.post('/api/v1/token/create', {
        name: 'Energy Credit',
        symbol: 'EC',
        decimals: 3,
        totalSupply: '1000000000', // 1 billion tokens
        description: 'Energy Credit tokens for peer-to-peer energy trading'
      })

      const tokenId = response.data.tokenId
      console.log('✅ Created Energy Credit token:', tokenId)
      
      return tokenId
    } catch (error) {
      console.error('Error creating energy token:', error)
      throw new BlockchainError('Failed to create energy credit token')
    }
  }

  private async getEscrowAddress(): Promise<string> {
    // In a real implementation, this would be a smart contract address
    // For now, we'll use a dedicated escrow wallet
    return process.env.ESCROW_WALLET_ADDRESS || 'maschain_escrow_address'
  }
}
