import { MasChainService } from './masChainService'
import { DatabaseService } from './databaseService'
import { ValidationError, NotFoundError, BlockchainError } from '../middleware/errorHandler'

export interface EnergyStats {
  totalProduction: number
  totalConsumption: number
  currentBalance: number
  creditsEarned: number
  creditsSpent: number
  carbonOffset: number
  lastUpdated: Date
}

export interface EnergyReading {
  id: string
  walletAddress: string
  meterId: string
  value: number
  type: 'production' | 'consumption'
  timestamp: Date
  signature: string
  verified: boolean
  blockchainTxId?: string
  createdAt: Date
}

export interface EnergyReadingInput {
  walletAddress: string
  meterId: string
  value: number
  type: 'production' | 'consumption'
  timestamp: Date
  signature: string
}

export interface MintCreditsInput {
  walletAddress: string
  amount: number
  readingIds: string[]
}

export interface ReadingQuery {
  limit: number
  offset: number
  type?: 'production' | 'consumption'
  startDate?: Date
  endDate?: Date
}

export class EnergyService {
  private masChainService: MasChainService
  private databaseService: DatabaseService

  constructor() {
    this.masChainService = new MasChainService()
    this.databaseService = new DatabaseService()
  }

  /**
   * Get user's energy statistics
   */
  async getUserEnergyStats(walletAddress: string): Promise<EnergyStats> {
    try {
      // Get stats from database
      const dbStats = await this.databaseService.getUserEnergyStats(walletAddress)
      
      // Get current balance from blockchain
      const blockchainBalance = await this.masChainService.getEnergyBalance(walletAddress)
      
      // Combine and return stats
      return {
        totalProduction: dbStats.totalProduction || 0,
        totalConsumption: dbStats.totalConsumption || 0,
        currentBalance: blockchainBalance,
        creditsEarned: dbStats.creditsEarned || 0,
        creditsSpent: dbStats.creditsSpent || 0,
        carbonOffset: this.calculateCarbonOffset(dbStats.totalProduction || 0),
        lastUpdated: new Date()
      }
    } catch (error) {
      console.error('Error fetching user energy stats:', error)
      throw new Error('Failed to fetch energy statistics')
    }
  }

  /**
   * Get energy readings for a user
   */
  async getEnergyReadings(
    walletAddress: string, 
    query: ReadingQuery
  ): Promise<{ readings: EnergyReading[]; total: number }> {
    try {
      return await this.databaseService.getEnergyReadings(walletAddress, query)
    } catch (error) {
      console.error('Error fetching energy readings:', error)
      throw new Error('Failed to fetch energy readings')
    }
  }

  /**
   * Submit a new energy reading
   */
  async submitEnergyReading(input: EnergyReadingInput): Promise<EnergyReading> {
    try {
      // Validate the reading
      await this.validateEnergyReading(input)

      // Verify the signature
      const isValidSignature = await this.masChainService.verifySignature(
        input.walletAddress,
        this.createReadingMessage(input),
        input.signature
      )

      if (!isValidSignature) {
        throw new ValidationError('Invalid signature for energy reading')
      }

      // Store in database
      const reading = await this.databaseService.createEnergyReading({
        ...input,
        id: this.generateReadingId(),
        verified: false, // Will be verified by oracle
        createdAt: new Date()
      })

      // Submit to oracle for verification
      await this.submitToOracle(reading)

      // Update user stats
      await this.updateUserStats(input.walletAddress, input.type, input.value)

      return reading
    } catch (error) {
      console.error('Error submitting energy reading:', error)
      if (error instanceof ValidationError) {
        throw error
      }
      throw new Error('Failed to submit energy reading')
    }
  }

  /**
   * Get user's energy credit balance
   */
  async getEnergyBalance(walletAddress: string): Promise<number> {
    try {
      return await this.masChainService.getEnergyBalance(walletAddress)
    } catch (error) {
      console.error('Error fetching energy balance:', error)
      throw new BlockchainError('Failed to fetch energy balance from blockchain')
    }
  }

  /**
   * Mint energy credits from verified production readings
   */
  async mintEnergyCredits(input: MintCreditsInput): Promise<{ txId: string; amount: number }> {
    try {
      // Verify all readings belong to user and are verified
      const readings = await this.databaseService.getReadingsByIds(input.readingIds)
      
      const invalidReadings = readings.filter(reading => 
        reading.walletAddress !== input.walletAddress || 
        !reading.verified ||
        reading.type !== 'production'
      )

      if (invalidReadings.length > 0) {
        throw new ValidationError('Some readings are invalid or not verified')
      }

      // Check if readings have already been used for minting
      const usedReadings = await this.databaseService.getUsedReadings(input.readingIds)
      if (usedReadings.length > 0) {
        throw new ValidationError('Some readings have already been used for minting')
      }

      // Calculate total energy from readings
      const totalEnergy = readings.reduce((sum, reading) => sum + reading.value, 0)
      
      if (Math.abs(totalEnergy - input.amount) > 0.001) {
        throw new ValidationError('Amount does not match total energy from readings')
      }

      // Mint tokens on blockchain
      const txId = await this.masChainService.mintEnergyCredits(
        input.walletAddress,
        input.amount
      )

      // Mark readings as used
      await this.databaseService.markReadingsAsUsed(input.readingIds, txId)

      // Update user stats
      await this.updateUserCreditsEarned(input.walletAddress, input.amount)

      return { txId, amount: input.amount }
    } catch (error) {
      console.error('Error minting energy credits:', error)
      if (error instanceof ValidationError) {
        throw error
      }
      throw new BlockchainError('Failed to mint energy credits')
    }
  }

  /**
   * Private helper methods
   */
  private async validateEnergyReading(input: EnergyReadingInput): Promise<void> {
    // Check if meter belongs to user
    const meter = await this.databaseService.getMeterByIdAndOwner(input.meterId, input.walletAddress)
    if (!meter) {
      throw new ValidationError('Meter not found or not owned by user')
    }

    // Validate reading value
    if (input.value < 0) {
      throw new ValidationError('Energy reading value cannot be negative')
    }

    if (input.value > meter.maxCapacity) {
      throw new ValidationError('Reading value exceeds meter capacity')
    }

    // Check timestamp is not too old or in future
    const now = new Date()
    const timeDiff = Math.abs(now.getTime() - input.timestamp.getTime())
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours

    if (timeDiff > maxAge) {
      throw new ValidationError('Reading timestamp is too old or in future')
    }

    // Check for duplicate readings
    const existingReading = await this.databaseService.findDuplicateReading(
      input.meterId,
      input.timestamp,
      input.value
    )

    if (existingReading) {
      throw new ValidationError('Duplicate reading detected')
    }
  }

  private createReadingMessage(input: EnergyReadingInput): string {
    return `${input.meterId}:${input.value}:${input.type}:${input.timestamp.toISOString()}`
  }

  private generateReadingId(): string {
    return `reading_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private async submitToOracle(reading: EnergyReading): Promise<void> {
    // Submit to oracle service for verification
    // This would typically involve calling an oracle API or smart contract
    console.log('Submitting reading to oracle for verification:', reading.id)
  }

  private async updateUserStats(
    walletAddress: string, 
    type: 'production' | 'consumption', 
    value: number
  ): Promise<void> {
    await this.databaseService.updateUserEnergyStats(walletAddress, type, value)
  }

  private async updateUserCreditsEarned(walletAddress: string, amount: number): Promise<void> {
    await this.databaseService.updateUserCreditsEarned(walletAddress, amount)
  }

  private calculateCarbonOffset(totalProduction: number): number {
    // Approximate carbon offset calculation
    // 1 kWh of renewable energy = ~0.5 kg CO2 offset
    return totalProduction * 0.5
  }
}
