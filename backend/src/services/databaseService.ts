import { Pool } from 'pg'
import { EnergyReading, EnergyStats, ReadingQuery } from './energyService'
import { EnergyOffer, OfferQuery, UserOfferQuery, Trade, MarketStats, PriceHistoryQuery, PricePoint } from './marketService'
import { UserSession, NonceRecord, ValidatorInfo } from './authService'

export class DatabaseService {
  private pool: Pool

  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/maschain_energy',
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
  }

  /**
   * Initialize database tables
   */
  async initialize(): Promise<void> {
    try {
      await this.createTables()
      console.log('✅ Database tables initialized')
    } catch (error) {
      console.error('❌ Failed to initialize database:', error)
      throw error
    }
  }

  /**
   * Energy-related methods
   */
  async getUserEnergyStats(walletAddress: string): Promise<Partial<EnergyStats>> {
    const query = `
      SELECT 
        COALESCE(SUM(CASE WHEN type = 'production' THEN value ELSE 0 END), 0) as total_production,
        COALESCE(SUM(CASE WHEN type = 'consumption' THEN value ELSE 0 END), 0) as total_consumption,
        COALESCE(credits_earned, 0) as credits_earned,
        COALESCE(credits_spent, 0) as credits_spent
      FROM energy_readings 
      LEFT JOIN user_stats ON user_stats.wallet_address = energy_readings.wallet_address
      WHERE energy_readings.wallet_address = $1
      GROUP BY credits_earned, credits_spent
    `
    
    const result = await this.pool.query(query, [walletAddress])
    const row = result.rows[0] || {}
    
    return {
      totalProduction: parseFloat(row.total_production || '0'),
      totalConsumption: parseFloat(row.total_consumption || '0'),
      creditsEarned: parseFloat(row.credits_earned || '0'),
      creditsSpent: parseFloat(row.credits_spent || '0')
    }
  }

  async getEnergyReadings(
    walletAddress: string, 
    query: ReadingQuery
  ): Promise<{ readings: EnergyReading[]; total: number }> {
    let whereClause = 'WHERE wallet_address = $1'
    const params: any[] = [walletAddress]
    let paramIndex = 2

    if (query.type) {
      whereClause += ` AND type = $${paramIndex}`
      params.push(query.type)
      paramIndex++
    }

    if (query.startDate) {
      whereClause += ` AND timestamp >= $${paramIndex}`
      params.push(query.startDate)
      paramIndex++
    }

    if (query.endDate) {
      whereClause += ` AND timestamp <= $${paramIndex}`
      params.push(query.endDate)
      paramIndex++
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM energy_readings ${whereClause}`
    const countResult = await this.pool.query(countQuery, params)
    const total = parseInt(countResult.rows[0].count)

    // Get readings with pagination
    const readingsQuery = `
      SELECT * FROM energy_readings 
      ${whereClause}
      ORDER BY timestamp DESC 
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    params.push(query.limit, query.offset)

    const readingsResult = await this.pool.query(readingsQuery, params)
    
    return {
      readings: readingsResult.rows.map(this.mapRowToEnergyReading),
      total
    }
  }

  async createEnergyReading(reading: EnergyReading): Promise<EnergyReading> {
    const query = `
      INSERT INTO energy_readings (
        id, wallet_address, meter_id, value, type, timestamp, signature, verified, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `
    
    const values = [
      reading.id,
      reading.walletAddress,
      reading.meterId,
      reading.value,
      reading.type,
      reading.timestamp,
      reading.signature,
      reading.verified,
      reading.createdAt
    ]

    const result = await this.pool.query(query, values)
    return this.mapRowToEnergyReading(result.rows[0])
  }

  /**
   * Market-related methods
   */
  async getActiveOffers(query: OfferQuery): Promise<{ offers: EnergyOffer[]; total: number }> {
    let whereClause = "WHERE status = 'active' AND expires_at > NOW()"
    const params: any[] = []
    let paramIndex = 1

    if (query.type) {
      whereClause += ` AND offer_type = $${paramIndex}`
      params.push(query.type)
      paramIndex++
    }

    if (query.priceRange?.min) {
      whereClause += ` AND price_per_kwh >= $${paramIndex}`
      params.push(query.priceRange.min)
      paramIndex++
    }

    if (query.priceRange?.max) {
      whereClause += ` AND price_per_kwh <= $${paramIndex}`
      params.push(query.priceRange.max)
      paramIndex++
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM energy_offers ${whereClause}`
    const countResult = await this.pool.query(countQuery, params)
    const total = parseInt(countResult.rows[0].count)

    // Get offers with pagination
    const offersQuery = `
      SELECT * FROM energy_offers 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    params.push(query.limit, query.offset)

    const offersResult = await this.pool.query(offersQuery, params)
    
    return {
      offers: offersResult.rows.map(this.mapRowToEnergyOffer),
      total
    }
  }

  async createOffer(offer: EnergyOffer): Promise<EnergyOffer> {
    const query = `
      INSERT INTO energy_offers (
        id, seller, energy_amount, price_per_kwh, offer_type, status, 
        created_at, expires_at, filled_amount, scheduled_for, recurring_pattern
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `
    
    const values = [
      offer.id,
      offer.seller,
      offer.energyAmount,
      offer.pricePerKwh,
      offer.offerType,
      offer.status,
      offer.createdAt,
      offer.expiresAt,
      offer.filledAmount,
      offer.scheduledFor,
      offer.recurringPattern ? JSON.stringify(offer.recurringPattern) : null
    ]

    const result = await this.pool.query(query, values)
    return this.mapRowToEnergyOffer(result.rows[0])
  }

  async getOfferById(offerId: string): Promise<EnergyOffer | null> {
    const query = 'SELECT * FROM energy_offers WHERE id = $1'
    const result = await this.pool.query(query, [offerId])
    
    return result.rows.length > 0 ? this.mapRowToEnergyOffer(result.rows[0]) : null
  }

  async updateOffer(offerId: string, updates: Partial<EnergyOffer>): Promise<void> {
    const setClause = Object.keys(updates)
      .map((key, index) => `${this.camelToSnake(key)} = $${index + 2}`)
      .join(', ')
    
    const query = `UPDATE energy_offers SET ${setClause} WHERE id = $1`
    const values = [offerId, ...Object.values(updates)]
    
    await this.pool.query(query, values)
  }

  async createTrade(trade: Trade): Promise<Trade> {
    const query = `
      INSERT INTO trades (
        id, buyer, seller, offer_id, amount, price_per_kwh, 
        total_price, status, executed_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `
    
    const values = [
      trade.id,
      trade.buyer,
      trade.seller,
      trade.offerId,
      trade.amount,
      trade.pricePerKwh,
      trade.totalPrice,
      trade.status,
      trade.executedAt
    ]

    const result = await this.pool.query(query, values)
    return this.mapRowToTrade(result.rows[0])
  }

  /**
   * Authentication-related methods
   */
  async createUserSession(session: UserSession): Promise<void> {
    const query = `
      INSERT INTO user_sessions (session_id, wallet_address, created_at, expires_at, is_active)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (session_id) DO UPDATE SET
        expires_at = EXCLUDED.expires_at,
        is_active = EXCLUDED.is_active
    `
    
    await this.pool.query(query, [
      session.sessionId,
      session.walletAddress,
      session.createdAt,
      session.expiresAt,
      session.isActive
    ])
  }

  async getUserSession(sessionId: string): Promise<UserSession | null> {
    const query = 'SELECT * FROM user_sessions WHERE session_id = $1'
    const result = await this.pool.query(query, [sessionId])
    
    return result.rows.length > 0 ? this.mapRowToUserSession(result.rows[0]) : null
  }

  /**
   * Helper methods
   */
  private async createTables(): Promise<void> {
    const queries = [
      // Energy readings table
      `CREATE TABLE IF NOT EXISTS energy_readings (
        id VARCHAR(255) PRIMARY KEY,
        wallet_address VARCHAR(255) NOT NULL,
        meter_id VARCHAR(255) NOT NULL,
        value DECIMAL(10,3) NOT NULL,
        type VARCHAR(20) NOT NULL CHECK (type IN ('production', 'consumption')),
        timestamp TIMESTAMP NOT NULL,
        signature TEXT NOT NULL,
        verified BOOLEAN DEFAULT FALSE,
        blockchain_tx_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_wallet_address (wallet_address),
        INDEX idx_meter_id (meter_id),
        INDEX idx_timestamp (timestamp)
      )`,
      
      // Energy offers table
      `CREATE TABLE IF NOT EXISTS energy_offers (
        id VARCHAR(255) PRIMARY KEY,
        seller VARCHAR(255) NOT NULL,
        energy_amount DECIMAL(10,3) NOT NULL,
        price_per_kwh DECIMAL(8,6) NOT NULL,
        offer_type VARCHAR(20) NOT NULL CHECK (offer_type IN ('immediate', 'scheduled', 'recurring')),
        status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'expired')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        filled_amount DECIMAL(10,3) DEFAULT 0,
        scheduled_for TIMESTAMP,
        recurring_pattern JSONB,
        blockchain_tx_id VARCHAR(255),
        INDEX idx_seller (seller),
        INDEX idx_status (status),
        INDEX idx_expires_at (expires_at)
      )`,
      
      // User sessions table
      `CREATE TABLE IF NOT EXISTS user_sessions (
        session_id VARCHAR(255) PRIMARY KEY,
        wallet_address VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_wallet_address (wallet_address)
      )`
    ]

    for (const query of queries) {
      await this.pool.query(query)
    }
  }

  private mapRowToEnergyReading(row: any): EnergyReading {
    return {
      id: row.id,
      walletAddress: row.wallet_address,
      meterId: row.meter_id,
      value: parseFloat(row.value),
      type: row.type,
      timestamp: row.timestamp,
      signature: row.signature,
      verified: row.verified,
      blockchainTxId: row.blockchain_tx_id,
      createdAt: row.created_at
    }
  }

  private mapRowToEnergyOffer(row: any): EnergyOffer {
    return {
      id: row.id,
      seller: row.seller,
      energyAmount: parseFloat(row.energy_amount),
      pricePerKwh: parseFloat(row.price_per_kwh),
      offerType: row.offer_type,
      status: row.status,
      createdAt: row.created_at,
      expiresAt: row.expires_at,
      filledAmount: parseFloat(row.filled_amount || '0'),
      scheduledFor: row.scheduled_for,
      recurringPattern: row.recurring_pattern ? JSON.parse(row.recurring_pattern) : undefined,
      blockchainTxId: row.blockchain_tx_id
    }
  }

  private mapRowToTrade(row: any): Trade {
    return {
      id: row.id,
      buyer: row.buyer,
      seller: row.seller,
      offerId: row.offer_id,
      amount: parseFloat(row.amount),
      pricePerKwh: parseFloat(row.price_per_kwh),
      totalPrice: parseFloat(row.total_price),
      status: row.status,
      executedAt: row.executed_at,
      blockchainTxId: row.blockchain_tx_id
    }
  }

  private mapRowToUserSession(row: any): UserSession {
    return {
      sessionId: row.session_id,
      walletAddress: row.wallet_address,
      createdAt: row.created_at,
      expiresAt: row.expires_at,
      isActive: row.is_active
    }
  }

  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
  }

  // Placeholder methods for missing functionality
  async getMeterByIdAndOwner(meterId: string, walletAddress: string): Promise<any> {
    // TODO: Implement meter ownership verification
    return { meterId, owner: walletAddress, maxCapacity: 1000 }
  }

  async findDuplicateReading(meterId: string, timestamp: Date, value: number): Promise<any> {
    // TODO: Implement duplicate reading detection
    return null
  }

  async getReadingsByIds(readingIds: string[]): Promise<EnergyReading[]> {
    // TODO: Implement reading retrieval by IDs
    return []
  }

  async getUsedReadings(readingIds: string[]): Promise<any[]> {
    // TODO: Implement used readings check
    return []
  }

  async markReadingsAsUsed(readingIds: string[], txId: string): Promise<void> {
    // TODO: Implement marking readings as used
  }

  async updateUserEnergyStats(walletAddress: string, type: string, value: number): Promise<void> {
    // TODO: Implement user stats update
  }

  async updateUserCreditsEarned(walletAddress: string, amount: number): Promise<void> {
    // TODO: Implement credits earned update
  }

  async getUserOffers(walletAddress: string, query: UserOfferQuery): Promise<{ offers: EnergyOffer[]; total: number }> {
    // TODO: Implement user offers retrieval
    return { offers: [], total: 0 }
  }

  async updateTrade(tradeId: string, updates: any): Promise<void> {
    const setClause = Object.keys(updates)
      .map((key, index) => `${this.camelToSnake(key)} = $${index + 2}`)
      .join(', ')

    const query = `UPDATE trades SET ${setClause} WHERE id = $1`
    const values = [tradeId, ...Object.values(updates)]

    await this.pool.query(query, values)
  }

  async updateOrderStatus(orderId: string, status: string): Promise<void> {
    const query = `UPDATE energy_offers SET status = $2 WHERE id = $1`
    await this.pool.query(query, [orderId, status])
  }

  async updateOrderPartialFill(orderId: string, remainingAmount: number): Promise<void> {
    const query = `
      UPDATE energy_offers
      SET filled_amount = energy_amount - $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `
    await this.pool.query(query, [orderId, remainingAmount])
  }

  async getActiveOrders(): Promise<any[]> {
    const query = `
      SELECT
        id, seller, energy_amount, price_per_kwh, offer_type,
        status, created_at, expires_at, filled_amount,
        (energy_amount - COALESCE(filled_amount, 0)) as remaining_amount
      FROM energy_offers
      WHERE status = 'active' AND expires_at > CURRENT_TIMESTAMP
      ORDER BY created_at ASC
    `

    const result = await this.pool.query(query)
    return result.rows.map(row => ({
      id: row.id,
      offerId: row.id,
      seller: row.seller,
      energyAmount: parseFloat(row.energy_amount),
      remainingAmount: parseFloat(row.remaining_amount),
      pricePerKwh: parseFloat(row.price_per_kwh),
      orderType: row.offer_type === 'buy' ? 'buy' : 'sell',
      timestamp: new Date(row.created_at),
      expiresAt: new Date(row.expires_at),
      status: row.status
    }))
  }

  async getMarketStats24h(): Promise<{ volume24h: number; lastPrice: number }> {
    const query = `
      SELECT
        COALESCE(SUM(amount), 0) as volume24h,
        (SELECT price_per_kwh FROM trades WHERE executed_at >= NOW() - INTERVAL '24 hours' ORDER BY executed_at DESC LIMIT 1) as last_price
      FROM trades
      WHERE executed_at >= NOW() - INTERVAL '24 hours' AND status = 'completed'
    `

    const result = await this.pool.query(query)
    const row = result.rows[0] || {}

    return {
      volume24h: parseFloat(row.volume24h || '0'),
      lastPrice: parseFloat(row.last_price || '0')
    }
  }

  async getMarketStats(): Promise<MarketStats> {
    // TODO: Implement market stats
    return {
      totalOffers: 0,
      totalVolumeTraded: 0,
      averagePrice: 0,
      activeOffers: 0,
      completedTrades: 0,
      totalParticipants: 0
    }
  }

  async getPriceHistory(query: PriceHistoryQuery): Promise<PricePoint[]> {
    // TODO: Implement price history
    return []
  }

  async updateExpiredOffers(): Promise<void> {
    // TODO: Implement expired offers update
  }

  async invalidateUserSession(sessionId: string): Promise<void> {
    // TODO: Implement session invalidation
  }

  async getNonceRecord(nonce: string, walletAddress: string): Promise<NonceRecord | null> {
    // TODO: Implement nonce record retrieval
    return null
  }

  async storeNonceRecord(record: NonceRecord): Promise<void> {
    // TODO: Implement nonce record storage
  }

  async getValidatorInfo(validatorAddress: string): Promise<ValidatorInfo | null> {
    // TODO: Implement validator info retrieval
    return null
  }

  async createValidator(validator: ValidatorInfo): Promise<void> {
    // TODO: Implement validator creation
  }

  async checkResourceOwnership(walletAddress: string, resourceId: string, resourceType: string): Promise<boolean> {
    // TODO: Implement resource ownership check
    return true
  }

  async getUserAuthInfo(walletAddress: string): Promise<any> {
    // TODO: Implement user auth info retrieval
    return null
  }

  async updateUserLastLogin(walletAddress: string): Promise<void> {
    // TODO: Implement last login update
  }

  // Transaction History Methods
  async createTransactionRecord(record: any): Promise<void> {
    const query = `
      INSERT INTO transaction_records (
        id, transaction_hash, block_number, block_hash, transaction_index,
        from_address, to_address, value, gas_used, gas_price, timestamp,
        status, transaction_type, metadata, confirmations
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
    `

    const values = [
      record.id, record.transactionHash, record.blockNumber, record.blockHash,
      record.transactionIndex, record.from, record.to, record.value,
      record.gasUsed, record.gasPrice, record.timestamp, record.status,
      record.transactionType, JSON.stringify(record.metadata), record.confirmations
    ]

    await this.pool.query(query, values)
  }

  async createUserActivity(activity: any): Promise<void> {
    const query = `
      INSERT INTO user_activities (
        id, user_id, activity_type, description, metadata,
        ip_address, user_agent, timestamp
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `

    const values = [
      activity.id, activity.userId, activity.activityType, activity.description,
      JSON.stringify(activity.metadata), activity.ipAddress, activity.userAgent,
      activity.timestamp
    ]

    await this.pool.query(query, values)
  }

  async createTradeHistory(trade: any): Promise<void> {
    const query = `
      INSERT INTO trade_history (
        id, buyer, seller, energy_amount, price_per_kwh, total_price,
        trading_fee, net_amount, executed_at, transaction_hash, status, order_ids
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    `

    const values = [
      trade.id, trade.buyer, trade.seller, trade.energyAmount, trade.pricePerKwh,
      trade.totalPrice, trade.tradingFee, trade.netAmount, trade.executedAt,
      trade.transactionHash, trade.status, JSON.stringify(trade.orderIds)
    ]

    await this.pool.query(query, values)
  }

  async createAuditEntry(entry: any): Promise<void> {
    const query = `
      INSERT INTO audit_trail (
        id, entity_type, entity_id, action, old_values, new_values,
        performed_by, timestamp, ip_address, reason
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `

    const values = [
      entry.id, entry.entityType, entry.entityId, entry.action,
      JSON.stringify(entry.oldValues), JSON.stringify(entry.newValues),
      entry.performedBy, entry.timestamp, entry.ipAddress, entry.reason
    ]

    await this.pool.query(query, values)
  }

  async getTransactionHistory(query: any): Promise<{ transactions: any[]; total: number }> {
    let whereClause = 'WHERE 1=1'
    const params: any[] = []
    let paramIndex = 1

    if (query.userId) {
      whereClause += ` AND (from_address = $${paramIndex} OR to_address = $${paramIndex})`
      params.push(query.userId)
      paramIndex++
    }

    if (query.transactionType) {
      whereClause += ` AND transaction_type = $${paramIndex}`
      params.push(query.transactionType)
      paramIndex++
    }

    if (query.status) {
      whereClause += ` AND status = $${paramIndex}`
      params.push(query.status)
      paramIndex++
    }

    if (query.startDate) {
      whereClause += ` AND timestamp >= $${paramIndex}`
      params.push(query.startDate)
      paramIndex++
    }

    if (query.endDate) {
      whereClause += ` AND timestamp <= $${paramIndex}`
      params.push(query.endDate)
      paramIndex++
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM transaction_records ${whereClause}`
    const countResult = await this.pool.query(countQuery, params)
    const total = parseInt(countResult.rows[0].count)

    // Get transactions with pagination
    const sortBy = query.sortBy || 'timestamp'
    const sortOrder = query.sortOrder || 'desc'
    const limit = query.limit || 50
    const offset = query.offset || 0

    const transactionsQuery = `
      SELECT * FROM transaction_records
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    params.push(limit, offset)

    const transactionsResult = await this.pool.query(transactionsQuery, params)

    return {
      transactions: transactionsResult.rows.map(row => ({
        id: row.id,
        transactionHash: row.transaction_hash,
        blockNumber: row.block_number,
        blockHash: row.block_hash,
        transactionIndex: row.transaction_index,
        from: row.from_address,
        to: row.to_address,
        value: row.value,
        gasUsed: row.gas_used,
        gasPrice: row.gas_price,
        timestamp: row.timestamp,
        status: row.status,
        transactionType: row.transaction_type,
        metadata: JSON.parse(row.metadata || '{}'),
        confirmations: row.confirmations
      })),
      total
    }
  }

  /**
   * Helper method to convert camelCase to snake_case
   */
  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
  }
}
