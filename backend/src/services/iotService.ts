import { EventEmitter } from 'events'
import { DatabaseService } from './databaseService'
import { OracleService } from './oracleService'
import { WebSocketManager } from './websocket'
import { ValidationError } from '../middleware/errorHandler'

// MQTT client interface (we'll implement a simple version)
interface MQTTClient {
  connect(): Promise<void>
  subscribe(topic: string): Promise<void>
  publish(topic: string, message: string): Promise<void>
  on(event: string, callback: Function): void
  disconnect(): Promise<void>
}

export interface IoTDevice {
  id: string
  deviceId: string
  owner: string
  deviceType: 'smart_meter' | 'solar_panel' | 'battery' | 'ev_charger'
  location: {
    latitude: number
    longitude: number
    address: string
  }
  specifications: {
    maxCapacity: number
    accuracy: number
    manufacturer: string
    model: string
    firmwareVersion: string
  }
  connectionStatus: 'online' | 'offline' | 'error'
  lastSeen: Date
  registeredAt: Date
  isVerified: boolean
}

export interface IoTReading {
  id: string
  deviceId: string
  timestamp: Date
  readingType: 'production' | 'consumption' | 'battery_level' | 'grid_export' | 'grid_import'
  value: number
  unit: 'kWh' | 'kW' | 'V' | 'A' | '%'
  quality: 'good' | 'fair' | 'poor'
  signature: string
  verified: boolean
  processedAt: Date
}

export interface DeviceAlert {
  id: string
  deviceId: string
  alertType: 'offline' | 'anomaly' | 'maintenance' | 'error'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: Date
  acknowledged: boolean
  resolvedAt?: Date
}

export interface AggregatedData {
  deviceId: string
  period: '1h' | '24h' | '7d' | '30d'
  totalProduction: number
  totalConsumption: number
  peakProduction: number
  peakConsumption: number
  averageProduction: number
  averageConsumption: number
  efficiency: number
  carbonOffset: number
  timestamp: Date
}

export class IoTService extends EventEmitter {
  private databaseService: DatabaseService
  private oracleService: OracleService
  private wsManager: WebSocketManager
  private mqttClient: MQTTClient | null = null
  
  // Device management
  private connectedDevices: Map<string, IoTDevice> = new Map()
  private deviceReadings: Map<string, IoTReading[]> = new Map()
  private deviceAlerts: Map<string, DeviceAlert[]> = new Map()
  
  // Data processing
  private processingQueue: IoTReading[] = []
  private isProcessing: boolean = false

  constructor() {
    super()
    this.databaseService = new DatabaseService()
    this.oracleService = new OracleService()
    this.wsManager = WebSocketManager.getInstance()
    
    // Start periodic tasks
    setInterval(() => this.processReadingQueue(), 5000) // Process every 5 seconds
    setInterval(() => this.checkDeviceHealth(), 30000) // Check health every 30 seconds
    setInterval(() => this.aggregateData(), 300000) // Aggregate every 5 minutes
  }

  /**
   * Initialize IoT service
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 Initializing IoT Service...')
      
      // Load registered devices
      const devices = await this.databaseService.getRegisteredDevices()
      for (const device of devices) {
        this.connectedDevices.set(device.deviceId, device)
      }
      
      // Initialize MQTT connection
      await this.initializeMQTT()
      
      console.log(`✅ IoT Service initialized with ${devices.length} registered devices`)
      this.emit('initialized')
      
    } catch (error) {
      console.error('❌ Failed to initialize IoT service:', error)
      throw error
    }
  }

  /**
   * Register a new IoT device
   */
  async registerDevice(deviceData: Omit<IoTDevice, 'id' | 'registeredAt' | 'lastSeen' | 'connectionStatus' | 'isVerified'>): Promise<IoTDevice> {
    try {
      // Validate device data
      this.validateDeviceData(deviceData)
      
      // Check if device already exists
      const existingDevice = await this.databaseService.getDeviceByDeviceId(deviceData.deviceId)
      if (existingDevice) {
        throw new ValidationError('Device already registered')
      }
      
      // Create device record
      const device: IoTDevice = {
        ...deviceData,
        id: this.generateDeviceId(),
        registeredAt: new Date(),
        lastSeen: new Date(),
        connectionStatus: 'offline',
        isVerified: false
      }
      
      // Store in database
      await this.databaseService.createDevice(device)
      
      // Add to connected devices
      this.connectedDevices.set(device.deviceId, device)
      
      // Subscribe to device MQTT topics
      await this.subscribeToDevice(device.deviceId)
      
      console.log(`📱 Device registered: ${device.deviceId} (${device.deviceType})`)
      this.emit('deviceRegistered', device)
      
      return device
      
    } catch (error) {
      console.error('Error registering device:', error)
      throw error
    }
  }

  /**
   * Process incoming IoT reading
   */
  async processReading(reading: Omit<IoTReading, 'id' | 'processedAt' | 'verified'>): Promise<IoTReading> {
    try {
      // Validate reading
      this.validateReading(reading)
      
      // Create reading record
      const iotReading: IoTReading = {
        ...reading,
        id: this.generateReadingId(),
        processedAt: new Date(),
        verified: false
      }
      
      // Add to processing queue
      this.processingQueue.push(iotReading)
      
      // Update device last seen
      const device = this.connectedDevices.get(reading.deviceId)
      if (device) {
        device.lastSeen = new Date()
        device.connectionStatus = 'online'
        await this.databaseService.updateDeviceStatus(device.deviceId, 'online', new Date())
      }
      
      console.log(`📊 Reading received: ${reading.deviceId} - ${reading.value} ${reading.unit}`)
      this.emit('readingReceived', iotReading)
      
      return iotReading
      
    } catch (error) {
      console.error('Error processing reading:', error)
      throw error
    }
  }

  /**
   * Get device readings with filtering
   */
  async getDeviceReadings(
    deviceId: string, 
    options: {
      startDate?: Date
      endDate?: Date
      readingType?: string
      limit?: number
      offset?: number
    } = {}
  ): Promise<{ readings: IoTReading[]; total: number }> {
    try {
      return await this.databaseService.getDeviceReadings(deviceId, options)
    } catch (error) {
      console.error('Error fetching device readings:', error)
      throw error
    }
  }

  /**
   * Get aggregated data for a device
   */
  async getAggregatedData(deviceId: string, period: '1h' | '24h' | '7d' | '30d'): Promise<AggregatedData> {
    try {
      return await this.databaseService.getAggregatedDeviceData(deviceId, period)
    } catch (error) {
      console.error('Error fetching aggregated data:', error)
      throw error
    }
  }

  /**
   * Get device alerts
   */
  async getDeviceAlerts(deviceId: string, acknowledged?: boolean): Promise<DeviceAlert[]> {
    try {
      return await this.databaseService.getDeviceAlerts(deviceId, acknowledged)
    } catch (error) {
      console.error('Error fetching device alerts:', error)
      throw error
    }
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId: string, userId: string): Promise<void> {
    try {
      await this.databaseService.acknowledgeAlert(alertId, userId)
      this.emit('alertAcknowledged', alertId)
    } catch (error) {
      console.error('Error acknowledging alert:', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async initializeMQTT(): Promise<void> {
    try {
      // Simple MQTT client implementation
      this.mqttClient = new SimpleMQTTClient()
      
      await this.mqttClient.connect()
      
      // Subscribe to general topics
      await this.mqttClient.subscribe('energy/+/readings')
      await this.mqttClient.subscribe('energy/+/status')
      await this.mqttClient.subscribe('energy/+/alerts')
      
      // Handle incoming messages
      this.mqttClient.on('message', (topic: string, message: string) => {
        this.handleMQTTMessage(topic, message)
      })
      
      console.log('✅ MQTT client connected')
      
    } catch (error) {
      console.warn('⚠️ MQTT connection failed, using fallback mode:', error)
      // Continue without MQTT for demo purposes
    }
  }

  private async subscribeToDevice(deviceId: string): Promise<void> {
    if (this.mqttClient) {
      await this.mqttClient.subscribe(`energy/${deviceId}/readings`)
      await this.mqttClient.subscribe(`energy/${deviceId}/status`)
      await this.mqttClient.subscribe(`energy/${deviceId}/alerts`)
    }
  }

  private handleMQTTMessage(topic: string, message: string): void {
    try {
      const parts = topic.split('/')
      const deviceId = parts[1]
      const messageType = parts[2]
      
      const data = JSON.parse(message)
      
      switch (messageType) {
        case 'readings':
          this.processReading({
            deviceId,
            timestamp: new Date(data.timestamp),
            readingType: data.readingType,
            value: data.value,
            unit: data.unit,
            quality: data.quality || 'good',
            signature: data.signature
          })
          break
          
        case 'status':
          this.updateDeviceStatus(deviceId, data.status)
          break
          
        case 'alerts':
          this.createAlert(deviceId, data)
          break
      }
      
    } catch (error) {
      console.error('Error handling MQTT message:', error)
    }
  }

  private async processReadingQueue(): Promise<void> {
    if (this.isProcessing || this.processingQueue.length === 0) return
    
    this.isProcessing = true
    
    try {
      const batch = this.processingQueue.splice(0, 10) // Process 10 at a time
      
      for (const reading of batch) {
        // Store in database
        await this.databaseService.createIoTReading(reading)
        
        // Submit to oracle for verification
        await this.oracleService.submitReading({
          meterId: reading.deviceId,
          reading: {
            value: reading.value,
            timestamp: reading.timestamp,
            unit: reading.unit as 'kWh' | 'MWh' | 'GWh'
          },
          signature: reading.signature,
          nonce: this.generateNonce()
        })
        
        // Check for anomalies
        await this.checkForAnomalies(reading)
        
        // Broadcast to WebSocket clients
        this.wsManager.broadcastEnergyUpdate('new_reading', reading)
      }
      
    } finally {
      this.isProcessing = false
    }
  }

  private async checkDeviceHealth(): Promise<void> {
    const now = new Date()
    const offlineThreshold = 5 * 60 * 1000 // 5 minutes
    
    for (const [deviceId, device] of this.connectedDevices.entries()) {
      const timeSinceLastSeen = now.getTime() - device.lastSeen.getTime()
      
      if (timeSinceLastSeen > offlineThreshold && device.connectionStatus === 'online') {
        device.connectionStatus = 'offline'
        await this.databaseService.updateDeviceStatus(deviceId, 'offline', now)
        
        // Create offline alert
        await this.createAlert(deviceId, {
          alertType: 'offline',
          severity: 'medium',
          message: `Device ${deviceId} has gone offline`
        })
        
        console.log(`📱 Device offline: ${deviceId}`)
      }
    }
  }

  private async aggregateData(): Promise<void> {
    // Aggregate data for all devices
    for (const [deviceId] of this.connectedDevices.entries()) {
      try {
        await this.databaseService.aggregateDeviceData(deviceId)
      } catch (error) {
        console.error(`Error aggregating data for device ${deviceId}:`, error)
      }
    }
  }

  private async checkForAnomalies(reading: IoTReading): Promise<void> {
    // Simple anomaly detection
    const recentReadings = await this.databaseService.getRecentReadings(reading.deviceId, 10)
    
    if (recentReadings.length >= 5) {
      const average = recentReadings.reduce((sum, r) => sum + r.value, 0) / recentReadings.length
      const deviation = Math.abs(reading.value - average) / average
      
      if (deviation > 0.5) { // 50% deviation
        await this.createAlert(reading.deviceId, {
          alertType: 'anomaly',
          severity: 'high',
          message: `Unusual reading detected: ${reading.value} ${reading.unit} (${(deviation * 100).toFixed(1)}% deviation)`
        })
      }
    }
  }

  private async createAlert(deviceId: string, alertData: any): Promise<void> {
    const alert: DeviceAlert = {
      id: this.generateAlertId(),
      deviceId,
      alertType: alertData.alertType,
      severity: alertData.severity,
      message: alertData.message,
      timestamp: new Date(),
      acknowledged: false
    }
    
    await this.databaseService.createDeviceAlert(alert)
    this.emit('alertCreated', alert)
    
    // Broadcast to WebSocket clients
    this.wsManager.broadcastEnergyUpdate('device_alert', alert)
  }

  private async updateDeviceStatus(deviceId: string, status: string): Promise<void> {
    const device = this.connectedDevices.get(deviceId)
    if (device) {
      device.connectionStatus = status as 'online' | 'offline' | 'error'
      device.lastSeen = new Date()
      await this.databaseService.updateDeviceStatus(deviceId, status, new Date())
    }
  }

  private validateDeviceData(deviceData: any): void {
    if (!deviceData.deviceId || deviceData.deviceId.length < 3) {
      throw new ValidationError('Device ID must be at least 3 characters')
    }
    
    if (!['smart_meter', 'solar_panel', 'battery', 'ev_charger'].includes(deviceData.deviceType)) {
      throw new ValidationError('Invalid device type')
    }
    
    if (!deviceData.specifications?.maxCapacity || deviceData.specifications.maxCapacity <= 0) {
      throw new ValidationError('Max capacity must be positive')
    }
  }

  private validateReading(reading: any): void {
    if (!reading.deviceId) {
      throw new ValidationError('Device ID is required')
    }
    
    if (!reading.value || reading.value < 0) {
      throw new ValidationError('Reading value must be non-negative')
    }
    
    if (!['production', 'consumption', 'battery_level', 'grid_export', 'grid_import'].includes(reading.readingType)) {
      throw new ValidationError('Invalid reading type')
    }
  }

  private generateDeviceId(): string {
    return `device_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateReadingId(): string {
    return `reading_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateNonce(): string {
    return Math.random().toString(36).substring(7)
  }
}

// Simple MQTT client implementation for demo
class SimpleMQTTClient implements MQTTClient {
  private eventEmitter = new EventEmitter()
  private connected = false

  async connect(): Promise<void> {
    // Simulate connection
    this.connected = true
    console.log('📡 MQTT client connected (simulated)')
  }

  async subscribe(topic: string): Promise<void> {
    if (!this.connected) throw new Error('Not connected')
    console.log(`📡 Subscribed to MQTT topic: ${topic}`)
  }

  async publish(topic: string, message: string): Promise<void> {
    if (!this.connected) throw new Error('Not connected')
    console.log(`📡 Published to MQTT topic ${topic}: ${message}`)
  }

  on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback)
  }

  async disconnect(): Promise<void> {
    this.connected = false
    console.log('📡 MQTT client disconnected')
  }
}
