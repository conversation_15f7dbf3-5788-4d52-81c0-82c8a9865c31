import { EventEmitter } from 'events'
import { DatabaseService } from './databaseService'
import { MasChainService } from './masChainService'
import { WebSocketManager } from './websocket'
import { ValidationError, ConflictError } from '../middleware/errorHandler'

export interface OrderBookEntry {
  id: string
  offerId: string
  seller: string
  energyAmount: number
  remainingAmount: number
  pricePerKwh: number
  orderType: 'buy' | 'sell'
  timestamp: Date
  expiresAt: Date
  status: 'active' | 'filled' | 'cancelled' | 'expired'
}

export interface TradeMatch {
  id: string
  buyOrderId: string
  sellOrderId: string
  buyer: string
  seller: string
  amount: number
  price: number
  timestamp: Date
  status: 'pending' | 'executed' | 'failed'
}

export interface OrderBookSnapshot {
  bids: OrderBookEntry[]  // Buy orders (highest price first)
  asks: OrderBookEntry[]  // Sell orders (lowest price first)
  lastPrice: number
  volume24h: number
  priceChange24h: number
  timestamp: Date
}

export interface MarketDepth {
  level: number
  price: number
  amount: number
  total: number
  orders: number
}

export interface PriceLevel {
  price: number
  amount: number
  orders: OrderBookEntry[]
}

export class OrderBookService extends EventEmitter {
  private databaseService: DatabaseService
  private masChainService: MasChainService
  private wsManager: WebSocketManager
  
  // In-memory order book for fast matching
  private bids: Map<string, PriceLevel> = new Map() // Buy orders by price
  private asks: Map<string, PriceLevel> = new Map() // Sell orders by price
  private orders: Map<string, OrderBookEntry> = new Map() // All orders by ID
  
  private lastPrice: number = 0
  private volume24h: number = 0
  private isProcessing: boolean = false

  constructor() {
    super()
    this.databaseService = new DatabaseService()
    this.masChainService = new MasChainService()
    this.wsManager = WebSocketManager.getInstance()
    
    // Start periodic cleanup of expired orders
    setInterval(() => this.cleanupExpiredOrders(), 60000) // Every minute
  }

  /**
   * Initialize order book from database
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 Initializing Order Book...')
      
      // Load active orders from database
      const activeOrders = await this.databaseService.getActiveOrders()
      
      for (const order of activeOrders) {
        this.addOrderToBook(order)
      }
      
      // Calculate initial market stats
      await this.updateMarketStats()
      
      console.log(`✅ Order Book initialized with ${activeOrders.length} active orders`)
      this.emit('initialized', this.getOrderBookSnapshot())
      
    } catch (error) {
      console.error('❌ Failed to initialize order book:', error)
      throw error
    }
  }

  /**
   * Add a new order to the order book
   */
  async addOrder(order: Omit<OrderBookEntry, 'id' | 'timestamp' | 'status'>): Promise<OrderBookEntry> {
    try {
      // Validate order
      this.validateOrder(order)
      
      // Create order entry
      const orderEntry: OrderBookEntry = {
        ...order,
        id: this.generateOrderId(),
        timestamp: new Date(),
        status: 'active',
        remainingAmount: order.energyAmount
      }
      
      // Store in database
      await this.databaseService.createOrder(orderEntry)
      
      // Add to in-memory order book
      this.addOrderToBook(orderEntry)
      
      // Try to match immediately
      await this.matchOrders(orderEntry)
      
      // Broadcast order book update
      this.broadcastOrderBookUpdate()
      
      console.log(`📝 Order added: ${orderEntry.id} - ${orderEntry.orderType} ${orderEntry.energyAmount} kWh @ ${orderEntry.pricePerKwh}`)
      
      return orderEntry
      
    } catch (error) {
      console.error('Error adding order:', error)
      throw error
    }
  }

  /**
   * Cancel an existing order
   */
  async cancelOrder(orderId: string, userId: string): Promise<void> {
    try {
      const order = this.orders.get(orderId)
      if (!order) {
        throw new ValidationError('Order not found')
      }
      
      if (order.seller !== userId) {
        throw new ValidationError('Not authorized to cancel this order')
      }
      
      if (order.status !== 'active') {
        throw new ValidationError('Order is not active')
      }
      
      // Remove from order book
      this.removeOrderFromBook(order)
      
      // Update in database
      await this.databaseService.updateOrderStatus(orderId, 'cancelled')
      
      // Broadcast update
      this.broadcastOrderBookUpdate()
      
      console.log(`❌ Order cancelled: ${orderId}`)
      this.emit('orderCancelled', order)
      
    } catch (error) {
      console.error('Error cancelling order:', error)
      throw error
    }
  }

  /**
   * Get current order book snapshot
   */
  getOrderBookSnapshot(): OrderBookSnapshot {
    const bids = Array.from(this.bids.entries())
      .map(([price, level]) => level.orders)
      .flat()
      .sort((a, b) => b.pricePerKwh - a.pricePerKwh) // Highest price first
      .slice(0, 20) // Top 20 levels
    
    const asks = Array.from(this.asks.entries())
      .map(([price, level]) => level.orders)
      .flat()
      .sort((a, b) => a.pricePerKwh - b.pricePerKwh) // Lowest price first
      .slice(0, 20) // Top 20 levels
    
    return {
      bids,
      asks,
      lastPrice: this.lastPrice,
      volume24h: this.volume24h,
      priceChange24h: this.calculatePriceChange24h(),
      timestamp: new Date()
    }
  }

  /**
   * Get market depth data
   */
  getMarketDepth(levels: number = 10): { bids: MarketDepth[]; asks: MarketDepth[] } {
    const bids = this.aggregateMarketDepth(this.bids, 'desc', levels)
    const asks = this.aggregateMarketDepth(this.asks, 'asc', levels)
    
    return { bids, asks }
  }

  /**
   * Private helper methods
   */
  private validateOrder(order: Omit<OrderBookEntry, 'id' | 'timestamp' | 'status'>): void {
    if (order.energyAmount <= 0) {
      throw new ValidationError('Energy amount must be positive')
    }
    
    if (order.pricePerKwh <= 0) {
      throw new ValidationError('Price must be positive')
    }
    
    if (order.expiresAt <= new Date()) {
      throw new ValidationError('Expiration time must be in the future')
    }
    
    if (!['buy', 'sell'].includes(order.orderType)) {
      throw new ValidationError('Invalid order type')
    }
  }

  private addOrderToBook(order: OrderBookEntry): void {
    const priceKey = order.pricePerKwh.toString()
    const book = order.orderType === 'buy' ? this.bids : this.asks
    
    if (!book.has(priceKey)) {
      book.set(priceKey, {
        price: order.pricePerKwh,
        amount: 0,
        orders: []
      })
    }
    
    const level = book.get(priceKey)!
    level.orders.push(order)
    level.amount += order.remainingAmount
    
    this.orders.set(order.id, order)
  }

  private removeOrderFromBook(order: OrderBookEntry): void {
    const priceKey = order.pricePerKwh.toString()
    const book = order.orderType === 'buy' ? this.bids : this.asks
    
    const level = book.get(priceKey)
    if (level) {
      level.orders = level.orders.filter(o => o.id !== order.id)
      level.amount -= order.remainingAmount
      
      if (level.orders.length === 0) {
        book.delete(priceKey)
      }
    }
    
    this.orders.delete(order.id)
    order.status = 'cancelled'
  }

  private async matchOrders(newOrder: OrderBookEntry): Promise<void> {
    if (this.isProcessing) return
    this.isProcessing = true
    
    try {
      const matches: TradeMatch[] = []
      const oppositeBook = newOrder.orderType === 'buy' ? this.asks : this.bids
      
      // Sort opposite book by best price
      const sortedPrices = Array.from(oppositeBook.keys())
        .map(price => parseFloat(price))
        .sort((a, b) => newOrder.orderType === 'buy' ? a - b : b - a)
      
      for (const price of sortedPrices) {
        if (newOrder.remainingAmount <= 0) break
        
        // Check if price matches
        const canMatch = newOrder.orderType === 'buy' 
          ? price <= newOrder.pricePerKwh 
          : price >= newOrder.pricePerKwh
        
        if (!canMatch) break
        
        const level = oppositeBook.get(price.toString())!
        
        for (const existingOrder of level.orders) {
          if (newOrder.remainingAmount <= 0) break
          if (existingOrder.remainingAmount <= 0) continue
          if (existingOrder.seller === newOrder.seller) continue // Can't trade with yourself
          
          // Calculate match amount
          const matchAmount = Math.min(newOrder.remainingAmount, existingOrder.remainingAmount)
          const matchPrice = existingOrder.pricePerKwh // Use existing order price
          
          // Create trade match
          const match: TradeMatch = {
            id: this.generateTradeId(),
            buyOrderId: newOrder.orderType === 'buy' ? newOrder.id : existingOrder.id,
            sellOrderId: newOrder.orderType === 'sell' ? newOrder.id : existingOrder.id,
            buyer: newOrder.orderType === 'buy' ? newOrder.seller : existingOrder.seller,
            seller: newOrder.orderType === 'sell' ? newOrder.seller : existingOrder.seller,
            amount: matchAmount,
            price: matchPrice,
            timestamp: new Date(),
            status: 'pending'
          }
          
          matches.push(match)
          
          // Update remaining amounts
          newOrder.remainingAmount -= matchAmount
          existingOrder.remainingAmount -= matchAmount
          
          // Update level amount
          level.amount -= matchAmount
          
          console.log(`🔄 Match found: ${matchAmount} kWh @ ${matchPrice} between ${match.buyer} and ${match.seller}`)
        }
        
        // Remove filled orders
        level.orders = level.orders.filter(o => o.remainingAmount > 0)
        if (level.orders.length === 0) {
          oppositeBook.delete(price.toString())
        }
      }
      
      // Execute all matches
      for (const match of matches) {
        await this.executeTrade(match)
      }
      
      // Update order status if filled
      if (newOrder.remainingAmount <= 0) {
        newOrder.status = 'filled'
        await this.databaseService.updateOrderStatus(newOrder.id, 'filled')
        this.orders.delete(newOrder.id)
      }
      
    } finally {
      this.isProcessing = false
    }
  }

  private async executeTrade(match: TradeMatch): Promise<void> {
    try {
      // Execute trade on blockchain
      const txId = await this.masChainService.executeTrade({
        buyer: match.buyer,
        seller: match.seller,
        energyAmount: match.amount,
        pricePerKwh: match.price,
        offerId: match.sellOrderId
      })
      
      // Update trade status
      match.status = 'executed'
      
      // Store trade in database
      await this.databaseService.createTrade({
        id: match.id,
        buyer: match.buyer,
        seller: match.seller,
        offerId: match.sellOrderId,
        amount: match.amount,
        pricePerKwh: match.price,
        totalPrice: match.amount * match.price,
        status: 'completed',
        executedAt: match.timestamp,
        blockchainTxId: txId
      })
      
      // Update market stats
      this.lastPrice = match.price
      this.volume24h += match.amount
      
      // Emit trade event
      this.emit('tradeExecuted', match)
      
      // Broadcast to WebSocket clients
      this.wsManager.broadcastMarketUpdate('trade_executed', match)
      
      console.log(`✅ Trade executed: ${match.amount} kWh @ ${match.price} (TX: ${txId})`)
      
    } catch (error) {
      console.error('Error executing trade:', error)
      match.status = 'failed'
      this.emit('tradeError', { match, error })
    }
  }

  private async cleanupExpiredOrders(): Promise<void> {
    const now = new Date()
    const expiredOrders = Array.from(this.orders.values())
      .filter(order => order.expiresAt <= now && order.status === 'active')
    
    for (const order of expiredOrders) {
      this.removeOrderFromBook(order)
      await this.databaseService.updateOrderStatus(order.id, 'expired')
      console.log(`⏰ Order expired: ${order.id}`)
    }
    
    if (expiredOrders.length > 0) {
      this.broadcastOrderBookUpdate()
    }
  }

  private broadcastOrderBookUpdate(): void {
    const snapshot = this.getOrderBookSnapshot()
    this.wsManager.broadcastMarketUpdate('orderbook_update', snapshot)
    this.emit('orderBookUpdated', snapshot)
  }

  private aggregateMarketDepth(book: Map<string, PriceLevel>, sort: 'asc' | 'desc', levels: number): MarketDepth[] {
    const sortedLevels = Array.from(book.entries())
      .sort(([a], [b]) => sort === 'asc' ? parseFloat(a) - parseFloat(b) : parseFloat(b) - parseFloat(a))
      .slice(0, levels)
    
    let total = 0
    return sortedLevels.map(([price, level], index) => {
      total += level.amount
      return {
        level: index + 1,
        price: parseFloat(price),
        amount: level.amount,
        total,
        orders: level.orders.length
      }
    })
  }

  private async updateMarketStats(): Promise<void> {
    // Calculate 24h volume and price change
    const stats = await this.databaseService.getMarketStats24h()
    this.volume24h = stats.volume24h || 0
    this.lastPrice = stats.lastPrice || 0
  }

  private calculatePriceChange24h(): number {
    // This would calculate price change from 24h ago
    // For now, return a placeholder
    return 0
  }

  private generateOrderId(): string {
    return `order_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }
}
