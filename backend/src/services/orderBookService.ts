import { EventEmitter } from 'events'
import { DatabaseService } from './databaseService'
import { MasChainService } from './masChainService'
import { WebSocketManager } from './websocket'
import { ValidationError, ConflictError } from '../middleware/errorHandler'

export interface OrderBookEntry {
  id: string
  offerId: string
  seller: string
  energyAmount: number
  remainingAmount: number
  pricePerKwh: number
  orderType: 'buy' | 'sell'
  timestamp: Date
  expiresAt: Date
  status: 'active' | 'filled' | 'cancelled' | 'expired'
}

export interface TradeMatch {
  id: string
  buyOrderId: string
  sellOrderId: string
  buyer: string
  seller: string
  amount: number
  price: number
  timestamp: Date
  status: 'pending' | 'executed' | 'failed'
}

export interface OrderBookSnapshot {
  bids: OrderBookEntry[]  // Buy orders (highest price first)
  asks: OrderBookEntry[]  // Sell orders (lowest price first)
  lastPrice: number
  volume24h: number
  priceChange24h: number
  timestamp: Date
}

export interface MarketDepth {
  level: number
  price: number
  amount: number
  total: number
  orders: number
}

export interface PriceLevel {
  price: number
  amount: number
  orders: OrderBookEntry[]
}

export class OrderBookService extends EventEmitter {
  private databaseService: DatabaseService
  private masChainService: MasChainService
  private wsManager: WebSocketManager
  
  // In-memory order book for fast matching
  private bids: Map<string, PriceLevel> = new Map() // Buy orders by price
  private asks: Map<string, PriceLevel> = new Map() // Sell orders by price
  private orders: Map<string, OrderBookEntry> = new Map() // All orders by ID
  
  private lastPrice: number = 0
  private volume24h: number = 0
  private isProcessing: boolean = false

  constructor() {
    super()
    this.databaseService = new DatabaseService()
    this.masChainService = new MasChainService()
    this.wsManager = WebSocketManager.getInstance()
    
    // Start periodic cleanup of expired orders
    setInterval(() => this.cleanupExpiredOrders(), 60000) // Every minute
  }

  /**
   * Initialize order book from database
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 Initializing Order Book...')
      
      // Load active orders from database
      const activeOrders = await this.databaseService.getActiveOrders()
      
      for (const order of activeOrders) {
        this.addOrderToBook(order)
      }
      
      // Calculate initial market stats
      await this.updateMarketStats()
      
      console.log(`✅ Order Book initialized with ${activeOrders.length} active orders`)
      this.emit('initialized', this.getOrderBookSnapshot())
      
    } catch (error) {
      console.error('❌ Failed to initialize order book:', error)
      throw error
    }
  }

  /**
   * Add a new order to the order book
   */
  async addOrder(order: Omit<OrderBookEntry, 'id' | 'timestamp' | 'status'>): Promise<OrderBookEntry> {
    try {
      // Validate order
      this.validateOrder(order)
      
      // Create order entry
      const orderEntry: OrderBookEntry = {
        ...order,
        id: this.generateOrderId(),
        timestamp: new Date(),
        status: 'active',
        remainingAmount: order.energyAmount
      }
      
      // Store in database
      await this.databaseService.createOrder(orderEntry)
      
      // Add to in-memory order book
      this.addOrderToBook(orderEntry)
      
      // Try to match immediately
      await this.matchOrders(orderEntry)
      
      // Broadcast order book update
      this.broadcastOrderBookUpdate()
      
      console.log(`📝 Order added: ${orderEntry.id} - ${orderEntry.orderType} ${orderEntry.energyAmount} kWh @ ${orderEntry.pricePerKwh}`)
      
      return orderEntry
      
    } catch (error) {
      console.error('Error adding order:', error)
      throw error
    }
  }

  /**
   * Cancel an existing order
   */
  async cancelOrder(orderId: string, userId: string): Promise<void> {
    try {
      const order = this.orders.get(orderId)
      if (!order) {
        throw new ValidationError('Order not found')
      }
      
      if (order.seller !== userId) {
        throw new ValidationError('Not authorized to cancel this order')
      }
      
      if (order.status !== 'active') {
        throw new ValidationError('Order is not active')
      }
      
      // Remove from order book
      this.removeOrderFromBook(order)
      
      // Update in database
      await this.databaseService.updateOrderStatus(orderId, 'cancelled')
      
      // Broadcast update
      this.broadcastOrderBookUpdate()
      
      console.log(`❌ Order cancelled: ${orderId}`)
      this.emit('orderCancelled', order)
      
    } catch (error) {
      console.error('Error cancelling order:', error)
      throw error
    }
  }

  /**
   * Get current order book snapshot
   */
  getOrderBookSnapshot(): OrderBookSnapshot {
    const bids = Array.from(this.bids.entries())
      .map(([price, level]) => level.orders)
      .flat()
      .sort((a, b) => b.pricePerKwh - a.pricePerKwh) // Highest price first
      .slice(0, 20) // Top 20 levels
    
    const asks = Array.from(this.asks.entries())
      .map(([price, level]) => level.orders)
      .flat()
      .sort((a, b) => a.pricePerKwh - b.pricePerKwh) // Lowest price first
      .slice(0, 20) // Top 20 levels
    
    return {
      bids,
      asks,
      lastPrice: this.lastPrice,
      volume24h: this.volume24h,
      priceChange24h: this.calculatePriceChange24h(),
      timestamp: new Date()
    }
  }

  /**
   * Get market depth data
   */
  getMarketDepth(levels: number = 10): { bids: MarketDepth[]; asks: MarketDepth[] } {
    const bids = this.aggregateMarketDepth(this.bids, 'desc', levels)
    const asks = this.aggregateMarketDepth(this.asks, 'asc', levels)
    
    return { bids, asks }
  }

  /**
   * Private helper methods
   */
  private validateOrder(order: Omit<OrderBookEntry, 'id' | 'timestamp' | 'status'>): void {
    if (order.energyAmount <= 0) {
      throw new ValidationError('Energy amount must be positive')
    }
    
    if (order.pricePerKwh <= 0) {
      throw new ValidationError('Price must be positive')
    }
    
    if (order.expiresAt <= new Date()) {
      throw new ValidationError('Expiration time must be in the future')
    }
    
    if (!['buy', 'sell'].includes(order.orderType)) {
      throw new ValidationError('Invalid order type')
    }
  }

  private addOrderToBook(order: OrderBookEntry): void {
    const priceKey = order.pricePerKwh.toString()
    const book = order.orderType === 'buy' ? this.bids : this.asks
    
    if (!book.has(priceKey)) {
      book.set(priceKey, {
        price: order.pricePerKwh,
        amount: 0,
        orders: []
      })
    }
    
    const level = book.get(priceKey)!
    level.orders.push(order)
    level.amount += order.remainingAmount
    
    this.orders.set(order.id, order)
  }

  private removeOrderFromBook(order: OrderBookEntry): void {
    const priceKey = order.pricePerKwh.toString()
    const book = order.orderType === 'buy' ? this.bids : this.asks
    
    const level = book.get(priceKey)
    if (level) {
      level.orders = level.orders.filter(o => o.id !== order.id)
      level.amount -= order.remainingAmount
      
      if (level.orders.length === 0) {
        book.delete(priceKey)
      }
    }
    
    this.orders.delete(order.id)
    order.status = 'cancelled'
  }

  /**
   * Enhanced order matching with sophisticated algorithms
   * Supports price-time priority, partial fills, and advanced matching strategies
   */
  private async matchOrders(newOrder: OrderBookEntry): Promise<void> {
    if (this.isProcessing) return
    this.isProcessing = true

    try {
      const matches: TradeMatch[] = []
      const oppositeBook = newOrder.orderType === 'buy' ? this.asks : this.bids

      // Enhanced price sorting with better precision handling
      const sortedPrices = Array.from(oppositeBook.keys())
        .map(price => parseFloat(price))
        .filter(price => !isNaN(price))
        .sort((a, b) => newOrder.orderType === 'buy' ? a - b : b - a)

      // Track cumulative matched volume for market impact analysis
      let cumulativeVolume = 0
      let weightedAveragePrice = 0

      for (const price of sortedPrices) {
        if (newOrder.remainingAmount <= 0) break

        // Enhanced price matching with tolerance for floating point precision
        const canMatch = this.canOrdersMatch(newOrder, price)
        if (!canMatch) break

        const level = oppositeBook.get(price.toString())!

        // Enhanced FIFO sorting with secondary priority by order size (pro-rata for large orders)
        level.orders.sort((a, b) => {
          const timeDiff = a.timestamp.getTime() - b.timestamp.getTime()
          if (Math.abs(timeDiff) < 1000) { // Within 1 second, use pro-rata
            return b.remainingAmount - a.remainingAmount // Larger orders first
          }
          return timeDiff // Time priority
        })

        for (const existingOrder of level.orders) {
          if (newOrder.remainingAmount <= 0) break
          if (existingOrder.remainingAmount <= 0) continue
          if (existingOrder.seller === newOrder.seller) continue // Can't trade with yourself

          // Calculate optimal match amount with minimum trade size enforcement
          const matchAmount = this.calculateMatchAmount(newOrder, existingOrder)
          if (matchAmount <= 0) continue

          const matchPrice = this.determineMatchPrice(newOrder, existingOrder)

          const match: TradeMatch = {
            id: this.generateTradeId(),
            buyOrderId: newOrder.orderType === 'buy' ? newOrder.id : existingOrder.id,
            sellOrderId: newOrder.orderType === 'sell' ? newOrder.id : existingOrder.id,
            buyer: newOrder.orderType === 'buy' ? newOrder.seller : existingOrder.seller,
            seller: newOrder.orderType === 'sell' ? newOrder.seller : existingOrder.seller,
            amount: matchAmount,
            price: matchPrice,
            timestamp: new Date(),
            status: 'pending'
          }

          matches.push(match)

          // Update remaining amounts with precision handling
          newOrder.remainingAmount = Math.max(0, newOrder.remainingAmount - matchAmount)
          existingOrder.remainingAmount = Math.max(0, existingOrder.remainingAmount - matchAmount)
          level.amount = Math.max(0, level.amount - matchAmount)

          // Track market statistics
          cumulativeVolume += matchAmount
          weightedAveragePrice = ((weightedAveragePrice * (cumulativeVolume - matchAmount)) + (matchPrice * matchAmount)) / cumulativeVolume

          console.log(`🔄 Enhanced Match: ${matchAmount} kWh @ ${matchPrice} (VWAP: ${weightedAveragePrice.toFixed(4)})`)
        }

        // Clean up filled orders and empty levels
        level.orders = level.orders.filter(o => o.remainingAmount > 0.001) // Dust threshold
        if (level.orders.length === 0) {
          oppositeBook.delete(price.toString())
        } else {
          // Update level amount
          level.amount = level.orders.reduce((sum, order) => sum + order.remainingAmount, 0)
        }
      }

      // Execute all matches with enhanced settlement
      await this.executeMatchesBatch(matches)

      // Update order status with partial fill handling
      await this.updateOrderAfterMatching(newOrder)

      // Emit market depth update if significant changes occurred
      if (matches.length > 0) {
        this.emit('marketDepthChanged', {
          matches: matches.length,
          volume: cumulativeVolume,
          vwap: weightedAveragePrice
        })
      }

    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Enhanced price matching with floating point precision tolerance
   */
  private canOrdersMatch(newOrder: OrderBookEntry, price: number): boolean {
    const tolerance = 0.0001 // 0.01 cent tolerance for floating point precision

    if (newOrder.orderType === 'buy') {
      return price <= (newOrder.pricePerKwh + tolerance)
    } else {
      return price >= (newOrder.pricePerKwh - tolerance)
    }
  }

  /**
   * Calculate optimal match amount with minimum trade size enforcement
   */
  private calculateMatchAmount(newOrder: OrderBookEntry, existingOrder: OrderBookEntry): number {
    const minTradeSize = 0.1 // Minimum 0.1 kWh trade size
    const maxTradeSize = Math.min(newOrder.remainingAmount, existingOrder.remainingAmount)

    if (maxTradeSize < minTradeSize) {
      return 0 // Trade too small
    }

    // Round to 3 decimal places to avoid floating point issues
    return Math.round(maxTradeSize * 1000) / 1000
  }

  /**
   * Determine match price using sophisticated pricing rules
   */
  private determineMatchPrice(newOrder: OrderBookEntry, existingOrder: OrderBookEntry): number {
    // Use existing order price (maker gets price priority)
    // This incentivizes liquidity provision
    return existingOrder.pricePerKwh
  }

  /**
   * Execute multiple trades in batch for better performance
   */
  private async executeMatchesBatch(matches: TradeMatch[]): Promise<void> {
    if (matches.length === 0) return

    // Group matches by seller for batch processing
    const matchesBySeller = new Map<string, TradeMatch[]>()

    for (const match of matches) {
      if (!matchesBySeller.has(match.seller)) {
        matchesBySeller.set(match.seller, [])
      }
      matchesBySeller.get(match.seller)!.push(match)
    }

    // Execute trades in parallel for each seller
    const executePromises = Array.from(matchesBySeller.values()).map(sellerMatches =>
      this.executeSellerMatches(sellerMatches)
    )

    await Promise.allSettled(executePromises)
  }

  /**
   * Execute all matches for a specific seller
   */
  private async executeSellerMatches(matches: TradeMatch[]): Promise<void> {
    for (const match of matches) {
      try {
        await this.executeTrade(match)
      } catch (error) {
        console.error(`Failed to execute trade ${match.id}:`, error)
        match.status = 'failed'
        this.emit('tradeError', { match, error })
      }
    }
  }

  /**
   * Update order status after matching with partial fill handling
   */
  private async updateOrderAfterMatching(order: OrderBookEntry): Promise<void> {
    if (order.remainingAmount <= 0.001) { // Dust threshold
      order.status = 'filled'
      await this.databaseService.updateOrderStatus(order.id, 'filled')
      this.orders.delete(order.id)
    } else if (order.remainingAmount < order.energyAmount) {
      // Partial fill
      order.status = 'active'
      await this.databaseService.updateOrderPartialFill(order.id, order.remainingAmount)
    }
  }

  private async executeTrade(match: TradeMatch): Promise<void> {
    try {
      // Execute trade on blockchain
      const txId = await this.masChainService.executeTrade({
        buyer: match.buyer,
        seller: match.seller,
        energyAmount: match.amount,
        pricePerKwh: match.price,
        offerId: match.sellOrderId
      })
      
      // Update trade status
      match.status = 'executed'
      
      // Store trade in database
      await this.databaseService.createTrade({
        id: match.id,
        buyer: match.buyer,
        seller: match.seller,
        offerId: match.sellOrderId,
        amount: match.amount,
        pricePerKwh: match.price,
        totalPrice: match.amount * match.price,
        status: 'completed',
        executedAt: match.timestamp,
        blockchainTxId: txId
      })
      
      // Update market stats
      this.lastPrice = match.price
      this.volume24h += match.amount
      
      // Emit trade event
      this.emit('tradeExecuted', match)
      
      // Broadcast to WebSocket clients
      this.wsManager.broadcastMarketUpdate('trade_executed', match)
      
      console.log(`✅ Trade executed: ${match.amount} kWh @ ${match.price} (TX: ${txId})`)
      
    } catch (error) {
      console.error('Error executing trade:', error)
      match.status = 'failed'
      this.emit('tradeError', { match, error })
    }
  }

  private async cleanupExpiredOrders(): Promise<void> {
    const now = new Date()
    const expiredOrders = Array.from(this.orders.values())
      .filter(order => order.expiresAt <= now && order.status === 'active')
    
    for (const order of expiredOrders) {
      this.removeOrderFromBook(order)
      await this.databaseService.updateOrderStatus(order.id, 'expired')
      console.log(`⏰ Order expired: ${order.id}`)
    }
    
    if (expiredOrders.length > 0) {
      this.broadcastOrderBookUpdate()
    }
  }

  private broadcastOrderBookUpdate(): void {
    const snapshot = this.getOrderBookSnapshot()
    this.wsManager.broadcastMarketUpdate('orderbook_update', snapshot)
    this.emit('orderBookUpdated', snapshot)
  }

  private aggregateMarketDepth(book: Map<string, PriceLevel>, sort: 'asc' | 'desc', levels: number): MarketDepth[] {
    const sortedLevels = Array.from(book.entries())
      .sort(([a], [b]) => sort === 'asc' ? parseFloat(a) - parseFloat(b) : parseFloat(b) - parseFloat(a))
      .slice(0, levels)
    
    let total = 0
    return sortedLevels.map(([price, level], index) => {
      total += level.amount
      return {
        level: index + 1,
        price: parseFloat(price),
        amount: level.amount,
        total,
        orders: level.orders.length
      }
    })
  }

  private async updateMarketStats(): Promise<void> {
    // Calculate 24h volume and price change
    const stats = await this.databaseService.getMarketStats24h()
    this.volume24h = stats.volume24h || 0
    this.lastPrice = stats.lastPrice || 0
  }

  private calculatePriceChange24h(): number {
    // This would calculate price change from 24h ago
    // For now, return a placeholder
    return 0
  }

  private generateOrderId(): string {
    return `order_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }
}
