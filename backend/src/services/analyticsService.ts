import { DatabaseService } from './databaseService'
import { WebSocketManager } from './websocket'

export interface MarketInsight {
  id: string
  insightType: 'price_trend' | 'volume_spike' | 'supply_demand' | 'efficiency_alert' | 'carbon_milestone'
  title: string
  description: string
  severity: 'info' | 'warning' | 'critical'
  data: any
  timestamp: Date
  expiresAt?: Date
}

export interface EnergyForecast {
  deviceId?: string
  userId?: string
  forecastType: 'production' | 'consumption' | 'price' | 'demand'
  period: '1h' | '24h' | '7d' | '30d'
  predictions: Array<{
    timestamp: Date
    value: number
    confidence: number
    upperBound: number
    lowerBound: number
  }>
  accuracy: number
  generatedAt: Date
}

export interface CarbonFootprint {
  userId: string
  period: '24h' | '7d' | '30d' | '1y'
  totalEnergyProduced: number
  totalEnergyConsumed: number
  renewablePercentage: number
  carbonSaved: number // kg CO2
  carbonEmitted: number // kg CO2
  netCarbonImpact: number // negative = carbon negative
  equivalentTrees: number
  equivalentCarMiles: number
  timestamp: Date
}

export interface PerformanceAnalytics {
  userId: string
  period: '24h' | '7d' | '30d' | '1y'
  energyEfficiency: number // percentage
  tradingEfficiency: number // percentage
  profitability: number // percentage
  marketShare: number // percentage
  reliability: number // percentage
  recommendations: string[]
  benchmarkComparison: {
    userValue: number
    averageValue: number
    topPercentile: number
    ranking: number
  }
  timestamp: Date
}

export interface MarketTrend {
  metric: 'price' | 'volume' | 'participants' | 'efficiency'
  period: '1h' | '24h' | '7d' | '30d'
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile'
  changePercentage: number
  currentValue: number
  previousValue: number
  dataPoints: Array<{
    timestamp: Date
    value: number
  }>
  analysis: string
  timestamp: Date
}

export interface AlertRule {
  id: string
  userId: string
  ruleType: 'price_threshold' | 'volume_threshold' | 'efficiency_drop' | 'device_offline' | 'carbon_target'
  conditions: {
    metric: string
    operator: '>' | '<' | '=' | '>=' | '<='
    value: number
    duration?: number // minutes
  }
  isActive: boolean
  lastTriggered?: Date
  createdAt: Date
}

export class AnalyticsService {
  private databaseService: DatabaseService
  private wsManager: WebSocketManager
  private insights: Map<string, MarketInsight> = new Map()
  private forecasts: Map<string, EnergyForecast> = new Map()

  constructor() {
    this.databaseService = new DatabaseService()
    this.wsManager = WebSocketManager.getInstance()
    
    // Start periodic analytics tasks
    setInterval(() => this.generateMarketInsights(), 300000) // Every 5 minutes
    setInterval(() => this.updateForecasts(), 900000) // Every 15 minutes
    setInterval(() => this.checkAlertRules(), 60000) // Every minute
    setInterval(() => this.calculateCarbonFootprints(), 3600000) // Every hour
  }

  /**
   * Generate market insights
   */
  async generateMarketInsights(): Promise<MarketInsight[]> {
    try {
      const insights: MarketInsight[] = []
      
      // Price trend analysis
      const priceTrend = await this.analyzePriceTrend()
      if (priceTrend) insights.push(priceTrend)
      
      // Volume spike detection
      const volumeSpike = await this.detectVolumeSpike()
      if (volumeSpike) insights.push(volumeSpike)
      
      // Supply/demand imbalance
      const supplyDemand = await this.analyzeSupplyDemand()
      if (supplyDemand) insights.push(supplyDemand)
      
      // Store insights
      for (const insight of insights) {
        this.insights.set(insight.id, insight)
        await this.databaseService.saveMarketInsight(insight)
        
        // Broadcast to interested users
        this.wsManager.broadcastMarketUpdate('market_insight', insight)
      }
      
      return insights
    } catch (error) {
      console.error('Error generating market insights:', error)
      return []
    }
  }

  /**
   * Generate energy forecast
   */
  async generateForecast(
    type: 'production' | 'consumption' | 'price' | 'demand',
    period: '1h' | '24h' | '7d' | '30d',
    deviceId?: string,
    userId?: string
  ): Promise<EnergyForecast> {
    try {
      // Get historical data
      const historicalData = await this.getHistoricalData(type, period, deviceId, userId)
      
      // Simple forecasting algorithm (in production, use ML models)
      const predictions = this.generatePredictions(historicalData, period)
      
      const forecast: EnergyForecast = {
        deviceId,
        userId,
        forecastType: type,
        period,
        predictions,
        accuracy: this.calculateForecastAccuracy(type, deviceId, userId),
        generatedAt: new Date()
      }
      
      // Cache forecast
      const key = `${type}_${period}_${deviceId || userId || 'global'}`
      this.forecasts.set(key, forecast)
      
      await this.databaseService.saveForecast(forecast)
      
      return forecast
    } catch (error) {
      console.error('Error generating forecast:', error)
      throw error
    }
  }

  /**
   * Calculate carbon footprint
   */
  async calculateCarbonFootprint(userId: string, period: '24h' | '7d' | '30d' | '1y'): Promise<CarbonFootprint> {
    try {
      const energyData = await this.databaseService.getUserEnergyData(userId, period)
      
      // Carbon calculation constants (kg CO2 per kWh)
      const GRID_CARBON_INTENSITY = 0.5 // Average grid carbon intensity
      const RENEWABLE_CARBON_INTENSITY = 0.05 // Solar/wind carbon intensity
      
      const totalEnergyProduced = energyData.totalProduction || 0
      const totalEnergyConsumed = energyData.totalConsumption || 0
      const renewablePercentage = totalEnergyProduced > 0 ? 
        (energyData.renewableProduction || totalEnergyProduced) / totalEnergyProduced * 100 : 0
      
      // Calculate carbon impact
      const carbonSaved = totalEnergyProduced * (GRID_CARBON_INTENSITY - RENEWABLE_CARBON_INTENSITY)
      const carbonEmitted = Math.max(0, totalEnergyConsumed - totalEnergyProduced) * GRID_CARBON_INTENSITY
      const netCarbonImpact = carbonEmitted - carbonSaved
      
      // Convert to equivalent metrics
      const equivalentTrees = Math.abs(netCarbonImpact) / 22 // 22kg CO2 per tree per year
      const equivalentCarMiles = Math.abs(netCarbonImpact) / 0.4 // 0.4kg CO2 per mile
      
      const footprint: CarbonFootprint = {
        userId,
        period,
        totalEnergyProduced,
        totalEnergyConsumed,
        renewablePercentage,
        carbonSaved,
        carbonEmitted,
        netCarbonImpact,
        equivalentTrees,
        equivalentCarMiles,
        timestamp: new Date()
      }
      
      await this.databaseService.saveCarbonFootprint(footprint)
      
      return footprint
    } catch (error) {
      console.error('Error calculating carbon footprint:', error)
      throw error
    }
  }

  /**
   * Analyze user performance
   */
  async analyzePerformance(userId: string, period: '24h' | '7d' | '30d' | '1y'): Promise<PerformanceAnalytics> {
    try {
      const userData = await this.databaseService.getUserPerformanceData(userId, period)
      const benchmarkData = await this.databaseService.getBenchmarkData(period)
      
      // Calculate efficiency metrics
      const energyEfficiency = this.calculateEnergyEfficiency(userData)
      const tradingEfficiency = this.calculateTradingEfficiency(userData)
      const profitability = this.calculateProfitability(userData)
      const marketShare = this.calculateMarketShare(userData, benchmarkData)
      const reliability = this.calculateReliability(userData)
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(userData, benchmarkData)
      
      // Benchmark comparison
      const benchmarkComparison = {
        userValue: energyEfficiency,
        averageValue: benchmarkData.averageEfficiency,
        topPercentile: benchmarkData.topPercentileEfficiency,
        ranking: await this.getUserRanking(userId, 'efficiency')
      }
      
      const analytics: PerformanceAnalytics = {
        userId,
        period,
        energyEfficiency,
        tradingEfficiency,
        profitability,
        marketShare,
        reliability,
        recommendations,
        benchmarkComparison,
        timestamp: new Date()
      }
      
      await this.databaseService.savePerformanceAnalytics(analytics)
      
      return analytics
    } catch (error) {
      console.error('Error analyzing performance:', error)
      throw error
    }
  }

  /**
   * Analyze market trends
   */
  async analyzeMarketTrends(): Promise<MarketTrend[]> {
    try {
      const trends: MarketTrend[] = []
      
      for (const metric of ['price', 'volume', 'participants', 'efficiency'] as const) {
        for (const period of ['1h', '24h', '7d', '30d'] as const) {
          const trend = await this.analyzeTrend(metric, period)
          if (trend) trends.push(trend)
        }
      }
      
      // Store trends
      for (const trend of trends) {
        await this.databaseService.saveMarketTrend(trend)
      }
      
      return trends
    } catch (error) {
      console.error('Error analyzing market trends:', error)
      return []
    }
  }

  /**
   * Create alert rule
   */
  async createAlertRule(rule: Omit<AlertRule, 'id' | 'createdAt'>): Promise<AlertRule> {
    try {
      const alertRule: AlertRule = {
        ...rule,
        id: this.generateAlertRuleId(),
        createdAt: new Date()
      }
      
      await this.databaseService.saveAlertRule(alertRule)
      
      return alertRule
    } catch (error) {
      console.error('Error creating alert rule:', error)
      throw error
    }
  }

  /**
   * Get user insights
   */
  async getUserInsights(userId: string): Promise<{
    insights: MarketInsight[]
    forecasts: EnergyForecast[]
    carbonFootprint: CarbonFootprint
    performance: PerformanceAnalytics
  }> {
    try {
      const [insights, forecasts, carbonFootprint, performance] = await Promise.all([
        this.databaseService.getUserInsights(userId),
        this.databaseService.getUserForecasts(userId),
        this.calculateCarbonFootprint(userId, '30d'),
        this.analyzePerformance(userId, '30d')
      ])
      
      return {
        insights,
        forecasts,
        carbonFootprint,
        performance
      }
    } catch (error) {
      console.error('Error getting user insights:', error)
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async analyzePriceTrend(): Promise<MarketInsight | null> {
    try {
      const priceData = await this.databaseService.getRecentPriceData('24h')
      
      if (priceData.length < 10) return null
      
      const trend = this.calculateTrend(priceData.map(d => d.price))
      const changePercentage = ((priceData[priceData.length - 1].price - priceData[0].price) / priceData[0].price) * 100
      
      if (Math.abs(changePercentage) > 10) {
        return {
          id: this.generateInsightId(),
          insightType: 'price_trend',
          title: `Significant Price ${changePercentage > 0 ? 'Increase' : 'Decrease'}`,
          description: `Energy prices have ${changePercentage > 0 ? 'increased' : 'decreased'} by ${Math.abs(changePercentage).toFixed(1)}% in the last 24 hours`,
          severity: Math.abs(changePercentage) > 20 ? 'critical' : 'warning',
          data: { changePercentage, trend, priceData: priceData.slice(-24) },
          timestamp: new Date()
        }
      }
      
      return null
    } catch (error) {
      console.error('Error analyzing price trend:', error)
      return null
    }
  }

  private async detectVolumeSpike(): Promise<MarketInsight | null> {
    try {
      const volumeData = await this.databaseService.getRecentVolumeData('24h')
      
      if (volumeData.length < 24) return null
      
      const currentVolume = volumeData[volumeData.length - 1].volume
      const averageVolume = volumeData.slice(0, -1).reduce((sum, d) => sum + d.volume, 0) / (volumeData.length - 1)
      
      const spikeRatio = currentVolume / averageVolume
      
      if (spikeRatio > 2) {
        return {
          id: this.generateInsightId(),
          insightType: 'volume_spike',
          title: 'Trading Volume Spike Detected',
          description: `Trading volume is ${spikeRatio.toFixed(1)}x higher than the 24-hour average`,
          severity: spikeRatio > 5 ? 'critical' : 'warning',
          data: { currentVolume, averageVolume, spikeRatio },
          timestamp: new Date()
        }
      }
      
      return null
    } catch (error) {
      console.error('Error detecting volume spike:', error)
      return null
    }
  }

  private async analyzeSupplyDemand(): Promise<MarketInsight | null> {
    try {
      const marketData = await this.databaseService.getCurrentMarketData()
      
      const supplyDemandRatio = marketData.totalSupply / marketData.totalDemand
      
      if (supplyDemandRatio < 0.8 || supplyDemandRatio > 1.2) {
        const imbalanceType = supplyDemandRatio < 1 ? 'shortage' : 'surplus'
        
        return {
          id: this.generateInsightId(),
          insightType: 'supply_demand',
          title: `Energy ${imbalanceType.charAt(0).toUpperCase() + imbalanceType.slice(1)} Detected`,
          description: `Current supply/demand ratio is ${supplyDemandRatio.toFixed(2)}, indicating a ${imbalanceType}`,
          severity: Math.abs(1 - supplyDemandRatio) > 0.3 ? 'critical' : 'warning',
          data: { supplyDemandRatio, totalSupply: marketData.totalSupply, totalDemand: marketData.totalDemand },
          timestamp: new Date()
        }
      }
      
      return null
    } catch (error) {
      console.error('Error analyzing supply/demand:', error)
      return null
    }
  }

  private generatePredictions(historicalData: any[], period: string): any[] {
    // Simple moving average prediction (in production, use ML models)
    const windowSize = Math.min(10, historicalData.length)
    const predictions = []
    
    const periodsToPredict = period === '1h' ? 12 : period === '24h' ? 24 : period === '7d' ? 7 : 30
    
    for (let i = 0; i < periodsToPredict; i++) {
      const recentValues = historicalData.slice(-windowSize).map(d => d.value)
      const average = recentValues.reduce((sum, val) => sum + val, 0) / recentValues.length
      const variance = recentValues.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / recentValues.length
      const stdDev = Math.sqrt(variance)
      
      predictions.push({
        timestamp: new Date(Date.now() + (i + 1) * this.getPeriodMs(period)),
        value: average + (Math.random() - 0.5) * stdDev * 0.1, // Add small random variation
        confidence: Math.max(0.5, 1 - (i * 0.05)), // Decreasing confidence over time
        upperBound: average + stdDev,
        lowerBound: average - stdDev
      })
    }
    
    return predictions
  }

  private async checkAlertRules(): Promise<void> {
    try {
      const activeRules = await this.databaseService.getActiveAlertRules()
      
      for (const rule of activeRules) {
        const shouldTrigger = await this.evaluateAlertRule(rule)
        
        if (shouldTrigger) {
          await this.triggerAlert(rule)
        }
      }
    } catch (error) {
      console.error('Error checking alert rules:', error)
    }
  }

  private async calculateCarbonFootprints(): Promise<void> {
    try {
      const activeUsers = await this.databaseService.getActiveUsers()
      
      for (const userId of activeUsers) {
        await this.calculateCarbonFootprint(userId, '24h')
      }
    } catch (error) {
      console.error('Error calculating carbon footprints:', error)
    }
  }

  private calculateTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' | 'volatile' {
    if (values.length < 2) return 'stable'
    
    const changes = values.slice(1).map((val, i) => val - values[i])
    const avgChange = changes.reduce((sum, change) => sum + change, 0) / changes.length
    const volatility = Math.sqrt(changes.reduce((sum, change) => sum + Math.pow(change - avgChange, 2), 0) / changes.length)
    
    if (volatility > Math.abs(avgChange) * 2) return 'volatile'
    if (avgChange > 0.01) return 'increasing'
    if (avgChange < -0.01) return 'decreasing'
    return 'stable'
  }

  private getPeriodMs(period: string): number {
    switch (period) {
      case '1h': return 5 * 60 * 1000 // 5 minutes
      case '24h': return 60 * 60 * 1000 // 1 hour
      case '7d': return 24 * 60 * 60 * 1000 // 1 day
      case '30d': return 24 * 60 * 60 * 1000 // 1 day
      default: return 60 * 60 * 1000
    }
  }

  private generateInsightId(): string {
    return `insight_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  private generateAlertRuleId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substring(7)}`
  }

  // Placeholder methods for complex calculations
  private async getHistoricalData(type: string, period: string, deviceId?: string, userId?: string): Promise<any[]> {
    return await this.databaseService.getHistoricalData(type, period, deviceId, userId)
  }

  private calculateForecastAccuracy(type: string, deviceId?: string, userId?: string): number {
    return 0.85 // Placeholder accuracy
  }

  private calculateEnergyEfficiency(userData: any): number {
    return userData.energyEfficiency || 85
  }

  private calculateTradingEfficiency(userData: any): number {
    return userData.tradingEfficiency || 78
  }

  private calculateProfitability(userData: any): number {
    return userData.profitability || 12
  }

  private calculateMarketShare(userData: any, benchmarkData: any): number {
    return userData.marketShare || 0.5
  }

  private calculateReliability(userData: any): number {
    return userData.reliability || 95
  }

  private generateRecommendations(userData: any, benchmarkData: any): string[] {
    return [
      'Consider increasing renewable energy production during peak hours',
      'Optimize trading timing based on market trends',
      'Improve energy storage efficiency'
    ]
  }

  private async getUserRanking(userId: string, metric: string): Promise<number> {
    return await this.databaseService.getUserRanking(userId, metric)
  }

  private async analyzeTrend(metric: string, period: string): Promise<MarketTrend | null> {
    // Placeholder implementation
    return null
  }

  private async evaluateAlertRule(rule: AlertRule): Promise<boolean> {
    // Placeholder implementation
    return false
  }

  private async triggerAlert(rule: AlertRule): Promise<void> {
    // Placeholder implementation
    console.log(`🚨 Alert triggered: ${rule.ruleType}`)
  }
}
