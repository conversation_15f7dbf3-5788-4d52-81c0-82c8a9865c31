import crypto from 'crypto'
import { MasChainService } from './masChainService'
import { DatabaseService } from './databaseService'
import { ValidationError, AuthenticationError } from '../middleware/errorHandler'

export interface UserSession {
  sessionId: string
  walletAddress: string
  createdAt: Date
  expiresAt: Date
  isActive: boolean
}

export interface NonceRecord {
  nonce: string
  walletAddress: string
  usedAt: Date
  expiresAt: Date
}

export interface ValidatorInfo {
  validatorAddress: string
  isActive: boolean
  registeredAt: Date
  reputation: number
}

export class AuthService {
  private masChainService: MasChainService
  private databaseService: DatabaseService
  private activeSessions: Map<string, UserSession> = new Map()
  private usedNonces: Map<string, NonceRecord> = new Map()

  constructor() {
    this.masChainService = new MasChainService()
    this.databaseService = new DatabaseService()
    
    // Clean up expired sessions and nonces periodically
    setInterval(() => {
      this.cleanupExpiredData()
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  /**
   * Create a new user session
   */
  async createSession(walletAddress: string, sessionId: string): Promise<UserSession> {
    try {
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      
      const session: UserSession = {
        sessionId,
        walletAddress,
        createdAt: new Date(),
        expiresAt,
        isActive: true
      }

      // Store in memory cache
      this.activeSessions.set(sessionId, session)

      // Store in database for persistence
      await this.databaseService.createUserSession(session)

      return session
    } catch (error) {
      console.error('Error creating session:', error)
      throw new Error('Failed to create user session')
    }
  }

  /**
   * Validate a user session
   */
  async validateSession(sessionId: string): Promise<boolean> {
    try {
      // Check memory cache first
      const cachedSession = this.activeSessions.get(sessionId)
      if (cachedSession) {
        if (cachedSession.isActive && new Date() < cachedSession.expiresAt) {
          return true
        } else {
          // Remove expired session
          this.activeSessions.delete(sessionId)
          return false
        }
      }

      // Check database
      const dbSession = await this.databaseService.getUserSession(sessionId)
      if (dbSession && dbSession.isActive && new Date() < dbSession.expiresAt) {
        // Cache the session
        this.activeSessions.set(sessionId, dbSession)
        return true
      }

      return false
    } catch (error) {
      console.error('Error validating session:', error)
      return false
    }
  }

  /**
   * Invalidate a user session
   */
  async invalidateSession(sessionId: string): Promise<void> {
    try {
      // Remove from memory cache
      this.activeSessions.delete(sessionId)

      // Mark as inactive in database
      await this.databaseService.invalidateUserSession(sessionId)
    } catch (error) {
      console.error('Error invalidating session:', error)
      throw new Error('Failed to invalidate session')
    }
  }

  /**
   * Check if a nonce has been used
   */
  async checkNonce(nonce: string, walletAddress: string): Promise<boolean> {
    try {
      const nonceKey = `${walletAddress}:${nonce}`
      
      // Check memory cache first
      const cachedNonce = this.usedNonces.get(nonceKey)
      if (cachedNonce && new Date() < cachedNonce.expiresAt) {
        return true // Nonce has been used
      }

      // Check database
      const dbNonce = await this.databaseService.getNonceRecord(nonce, walletAddress)
      if (dbNonce && new Date() < dbNonce.expiresAt) {
        return true // Nonce has been used
      }

      return false // Nonce is fresh
    } catch (error) {
      console.error('Error checking nonce:', error)
      return true // Err on the side of caution
    }
  }

  /**
   * Store a used nonce
   */
  async storeNonce(nonce: string, walletAddress: string): Promise<void> {
    try {
      const nonceKey = `${walletAddress}:${nonce}`
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
      
      const nonceRecord: NonceRecord = {
        nonce,
        walletAddress,
        usedAt: new Date(),
        expiresAt
      }

      // Store in memory cache
      this.usedNonces.set(nonceKey, nonceRecord)

      // Store in database
      await this.databaseService.storeNonceRecord(nonceRecord)
    } catch (error) {
      console.error('Error storing nonce:', error)
      throw new Error('Failed to store nonce')
    }
  }

  /**
   * Verify wallet signature
   */
  async verifyWalletSignature(
    walletAddress: string, 
    message: string, 
    signature: string
  ): Promise<boolean> {
    try {
      return await this.masChainService.verifySignature(walletAddress, message, signature)
    } catch (error) {
      console.error('Error verifying wallet signature:', error)
      return false
    }
  }

  /**
   * Validate if an address is a registered validator
   */
  async validateValidator(validatorAddress: string): Promise<boolean> {
    try {
      const validator = await this.databaseService.getValidatorInfo(validatorAddress)
      return validator ? validator.isActive : false
    } catch (error) {
      console.error('Error validating validator:', error)
      return false
    }
  }

  /**
   * Register a new validator
   */
  async registerValidator(validatorAddress: string): Promise<ValidatorInfo> {
    try {
      // Verify the address is a valid wallet
      const balance = await this.masChainService.getWalletBalance(validatorAddress)
      if (balance < 0) {
        throw new ValidationError('Invalid validator wallet address')
      }

      const validator: ValidatorInfo = {
        validatorAddress,
        isActive: true,
        registeredAt: new Date(),
        reputation: 100 // Starting reputation
      }

      await this.databaseService.createValidator(validator)
      return validator
    } catch (error) {
      console.error('Error registering validator:', error)
      throw new Error('Failed to register validator')
    }
  }

  /**
   * Check if user owns a specific resource
   */
  async checkResourceOwnership(
    walletAddress: string, 
    resourceId: string, 
    resourceType: string
  ): Promise<boolean> {
    try {
      return await this.databaseService.checkResourceOwnership(
        walletAddress, 
        resourceId, 
        resourceType
      )
    } catch (error) {
      console.error('Error checking resource ownership:', error)
      return false
    }
  }

  /**
   * Generate a secure nonce
   */
  generateNonce(): string {
    return crypto.randomBytes(16).toString('hex')
  }

  /**
   * Generate a challenge message for wallet authentication
   */
  generateChallengeMessage(walletAddress: string, nonce: string): string {
    const timestamp = Date.now()
    return `Please sign this message to authenticate with MasChain Energy Trading Platform.\n\nWallet: ${walletAddress}\nNonce: ${nonce}\nTimestamp: ${timestamp}\n\nThis request will not trigger any blockchain transaction or cost any gas fees.`
  }

  /**
   * Validate challenge response
   */
  async validateChallengeResponse(
    walletAddress: string,
    nonce: string,
    signature: string,
    timestamp: number
  ): Promise<boolean> {
    try {
      // Check timestamp is recent (within 5 minutes)
      const now = Date.now()
      const timeDiff = Math.abs(now - timestamp)
      if (timeDiff > 5 * 60 * 1000) {
        return false
      }

      // Check nonce hasn't been used
      const isNonceUsed = await this.checkNonce(nonce, walletAddress)
      if (isNonceUsed) {
        return false
      }

      // Verify signature
      const message = this.generateChallengeMessage(walletAddress, nonce)
      const isValidSignature = await this.verifyWalletSignature(walletAddress, message, signature)
      
      if (isValidSignature) {
        // Store nonce to prevent replay
        await this.storeNonce(nonce, walletAddress)
        return true
      }

      return false
    } catch (error) {
      console.error('Error validating challenge response:', error)
      return false
    }
  }

  /**
   * Get user authentication info
   */
  async getUserAuthInfo(walletAddress: string): Promise<{
    isRegistered: boolean
    lastLogin?: Date
    sessionCount: number
  }> {
    try {
      const authInfo = await this.databaseService.getUserAuthInfo(walletAddress)
      return {
        isRegistered: authInfo ? true : false,
        lastLogin: authInfo?.lastLogin,
        sessionCount: authInfo?.sessionCount || 0
      }
    } catch (error) {
      console.error('Error fetching user auth info:', error)
      return {
        isRegistered: false,
        sessionCount: 0
      }
    }
  }

  /**
   * Update user last login
   */
  async updateUserLastLogin(walletAddress: string): Promise<void> {
    try {
      await this.databaseService.updateUserLastLogin(walletAddress)
    } catch (error) {
      console.error('Error updating user last login:', error)
    }
  }

  /**
   * Clean up expired sessions and nonces
   */
  private cleanupExpiredData(): void {
    const now = new Date()

    // Clean up expired sessions
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(sessionId)
      }
    }

    // Clean up expired nonces
    for (const [nonceKey, nonceRecord] of this.usedNonces.entries()) {
      if (now > nonceRecord.expiresAt) {
        this.usedNonces.delete(nonceKey)
      }
    }

    console.log(`🧹 Cleaned up expired auth data. Active sessions: ${this.activeSessions.size}, Used nonces: ${this.usedNonces.size}`)
  }
}
