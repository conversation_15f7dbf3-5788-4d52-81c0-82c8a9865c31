import { Pool, PoolConfig } from 'pg'

interface DatabaseConfig extends PoolConfig {
  connectionString?: string
}

/**
 * Optimized database configuration for production performance
 */
export function getDatabaseConfig(): DatabaseConfig {
  const isProduction = process.env.NODE_ENV === 'production'
  
  const baseConfig: DatabaseConfig = {
    // Connection settings
    connectionString: process.env.DATABASE_URL,
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'maschain_energy',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    
    // SSL configuration
    ssl: isProduction ? { rejectUnauthorized: false } : false,
    
    // Connection pool settings optimized for performance
    min: parseInt(process.env.DB_POOL_MIN || '2'),
    max: parseInt(process.env.DB_POOL_MAX || isProduction ? '20' : '10'),
    
    // Connection timeout settings
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '5000'),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
    
    // Query timeout
    query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
    
    // Statement timeout (PostgreSQL specific)
    statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '30000'),
    
    // Keep alive settings
    keepAlive: true,
    keepAliveInitialDelayMillis: 10000,
    
    // Application name for monitoring
    application_name: 'maschain-energy-api',
    
    // Additional performance settings
    options: isProduction ? '-c default_transaction_isolation=read_committed' : undefined
  }

  return baseConfig
}

/**
 * Create optimized database pool
 */
export function createDatabasePool(): Pool {
  const config = getDatabaseConfig()
  const pool = new Pool(config)

  // Pool event handlers for monitoring
  pool.on('connect', (client) => {
    console.log('🔗 New database client connected')
    
    // Set session-level optimizations
    client.query(`
      SET statement_timeout = '30s';
      SET lock_timeout = '10s';
      SET idle_in_transaction_session_timeout = '60s';
      SET default_transaction_isolation = 'read committed';
    `).catch(err => {
      console.error('Error setting session parameters:', err)
    })
  })

  pool.on('acquire', () => {
    console.log('📊 Database client acquired from pool')
  })

  pool.on('release', () => {
    console.log('📤 Database client released back to pool')
  })

  pool.on('remove', () => {
    console.log('🗑️ Database client removed from pool')
  })

  pool.on('error', (err) => {
    console.error('💥 Database pool error:', err)
  })

  // Graceful shutdown handler
  process.on('SIGINT', async () => {
    console.log('🛑 Shutting down database pool...')
    await pool.end()
    console.log('✅ Database pool closed')
    process.exit(0)
  })

  process.on('SIGTERM', async () => {
    console.log('🛑 Shutting down database pool...')
    await pool.end()
    console.log('✅ Database pool closed')
    process.exit(0)
  })

  return pool
}

/**
 * Database health check
 */
export async function checkDatabaseHealth(pool: Pool): Promise<{
  healthy: boolean
  latency: number
  activeConnections: number
  idleConnections: number
  totalConnections: number
}> {
  const startTime = Date.now()
  
  try {
    // Simple health check query
    await pool.query('SELECT 1')
    const latency = Date.now() - startTime
    
    // Get connection pool stats
    const totalConnections = pool.totalCount
    const idleConnections = pool.idleCount
    const activeConnections = totalConnections - idleConnections
    
    return {
      healthy: true,
      latency,
      activeConnections,
      idleConnections,
      totalConnections
    }
  } catch (error) {
    console.error('Database health check failed:', error)
    return {
      healthy: false,
      latency: Date.now() - startTime,
      activeConnections: 0,
      idleConnections: 0,
      totalConnections: 0
    }
  }
}

/**
 * Execute query with performance monitoring
 */
export async function executeQuery(
  pool: Pool, 
  query: string, 
  params?: any[]
): Promise<any> {
  const startTime = Date.now()
  const client = await pool.connect()
  
  try {
    const result = await client.query(query, params)
    const duration = Date.now() - startTime
    
    // Log slow queries
    if (duration > 1000) {
      console.warn(`🐌 Slow query detected (${duration}ms):`, query.substring(0, 100))
    }
    
    return result
  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ Query failed after ${duration}ms:`, error)
    throw error
  } finally {
    client.release()
  }
}

/**
 * Execute transaction with retry logic
 */
export async function executeTransaction(
  pool: Pool,
  queries: Array<{ query: string; params?: any[] }>,
  maxRetries: number = 3
): Promise<any[]> {
  let attempt = 0
  
  while (attempt < maxRetries) {
    const client = await pool.connect()
    
    try {
      await client.query('BEGIN')
      
      const results = []
      for (const { query, params } of queries) {
        const result = await client.query(query, params)
        results.push(result)
      }
      
      await client.query('COMMIT')
      return results
      
    } catch (error) {
      await client.query('ROLLBACK')
      attempt++
      
      if (attempt >= maxRetries) {
        console.error(`❌ Transaction failed after ${maxRetries} attempts:`, error)
        throw error
      }
      
      // Exponential backoff
      const delay = Math.pow(2, attempt) * 100
      await new Promise(resolve => setTimeout(resolve, delay))
      
    } finally {
      client.release()
    }
  }
  
  throw new Error('Transaction failed after maximum retries')
}

/**
 * Batch insert with optimized performance
 */
export async function batchInsert(
  pool: Pool,
  table: string,
  columns: string[],
  rows: any[][],
  batchSize: number = 1000
): Promise<void> {
  if (rows.length === 0) return
  
  const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ')
  const baseQuery = `INSERT INTO ${table} (${columns.join(', ')}) VALUES `
  
  for (let i = 0; i < rows.length; i += batchSize) {
    const batch = rows.slice(i, i + batchSize)
    const values: any[] = []
    const valuePlaceholders: string[] = []
    
    batch.forEach((row, rowIndex) => {
      const rowPlaceholders = columns.map((_, colIndex) => {
        const paramIndex = rowIndex * columns.length + colIndex + 1
        values.push(row[colIndex])
        return `$${paramIndex}`
      }).join(', ')
      
      valuePlaceholders.push(`(${rowPlaceholders})`)
    })
    
    const query = baseQuery + valuePlaceholders.join(', ')
    await executeQuery(pool, query, values)
  }
}

/**
 * Get database performance statistics
 */
export async function getDatabaseStats(pool: Pool): Promise<any> {
  try {
    const queries = [
      // Connection stats
      `SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections
       FROM pg_stat_activity 
       WHERE datname = current_database()`,
      
      // Database size
      `SELECT pg_size_pretty(pg_database_size(current_database())) as database_size`,
      
      // Table sizes
      `SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
       FROM pg_tables 
       WHERE schemaname = 'public' 
       ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
       LIMIT 10`,
      
      // Index usage
      `SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan,
        idx_tup_read,
        idx_tup_fetch
       FROM pg_stat_user_indexes 
       ORDER BY idx_scan DESC 
       LIMIT 10`
    ]
    
    const results = await Promise.all(
      queries.map(query => executeQuery(pool, query))
    )
    
    return {
      connections: results[0].rows[0],
      databaseSize: results[1].rows[0],
      tableSizes: results[2].rows,
      indexUsage: results[3].rows,
      poolStats: {
        total: pool.totalCount,
        idle: pool.idleCount,
        waiting: pool.waitingCount
      }
    }
  } catch (error) {
    console.error('Error getting database stats:', error)
    return null
  }
}
