import { Request, Response, NextFunction } from 'express'

export interface ApiError extends Error {
  statusCode?: number
  code?: string
  details?: any
  isOperational?: boolean
}

/**
 * Custom error classes for different types of errors
 */
export class ValidationError extends Error implements ApiError {
  statusCode = 400
  code = 'VALIDATION_ERROR'
  isOperational = true

  constructor(message: string, public details?: any) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends Error implements ApiError {
  statusCode = 401
  code = 'AUTHENTICATION_ERROR'
  isOperational = true

  constructor(message: string = 'Authentication required') {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error implements ApiError {
  statusCode = 403
  code = 'AUTHORIZATION_ERROR'
  isOperational = true

  constructor(message: string = 'Access denied') {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends Error implements ApiError {
  statusCode = 404
  code = 'NOT_FOUND'
  isOperational = true

  constructor(message: string = 'Resource not found') {
    super(message)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends Error implements ApiError {
  statusCode = 409
  code = 'CONFLICT'
  isOperational = true

  constructor(message: string, public details?: any) {
    super(message)
    this.name = 'ConflictError'
  }
}

export class BlockchainError extends Error implements ApiError {
  statusCode = 502
  code = 'BLOCKCHAIN_ERROR'
  isOperational = true

  constructor(message: string, public details?: any) {
    super(message)
    this.name = 'BlockchainError'
  }
}

export class InternalServerError extends Error implements ApiError {
  statusCode = 500
  code = 'INTERNAL_SERVER_ERROR'
  isOperational = false

  constructor(message: string = 'Internal server error', public details?: any) {
    super(message)
    this.name = 'InternalServerError'
  }
}

/**
 * Main error handling middleware
 */
export const errorHandler = (
  error: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log error details
  console.error('Error occurred:', {
    name: error.name,
    message: error.message,
    statusCode: error.statusCode,
    code: error.code,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  })

  // Don't leak error details in production for non-operational errors
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isOperational = error.isOperational || false

  let statusCode = error.statusCode || 500
  let message = error.message
  let code = error.code || 'UNKNOWN_ERROR'
  let details = error.details

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400
    code = 'VALIDATION_ERROR'
  } else if (error.name === 'CastError') {
    statusCode = 400
    code = 'INVALID_ID'
    message = 'Invalid ID format'
  } else if (error.name === 'MongoError' && (error as any).code === 11000) {
    statusCode = 409
    code = 'DUPLICATE_ENTRY'
    message = 'Duplicate entry'
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    code = 'INVALID_TOKEN'
    message = 'Invalid authentication token'
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401
    code = 'TOKEN_EXPIRED'
    message = 'Authentication token expired'
  }

  // Sanitize error response for production
  if (!isDevelopment && !isOperational) {
    message = 'Internal server error'
    details = undefined
  }

  // Prepare error response
  const errorResponse: any = {
    error: message,
    code,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method
  }

  // Add details if available and appropriate
  if (details && (isDevelopment || isOperational)) {
    errorResponse.details = details
  }

  // Add stack trace in development
  if (isDevelopment) {
    errorResponse.stack = error.stack
  }

  // Add request ID if available
  if (req.headers['x-request-id']) {
    errorResponse.requestId = req.headers['x-request-id']
  }

  res.status(statusCode).json(errorResponse)
}

/**
 * Async error wrapper to catch async errors in route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new NotFoundError(`Route ${req.method} ${req.path} not found`)
  next(error)
}

/**
 * Validation error formatter
 */
export const formatValidationError = (errors: any[]) => {
  return errors.map(error => ({
    field: error.param || error.path,
    message: error.msg || error.message,
    value: error.value
  }))
}

/**
 * Create standardized API response
 */
export const createApiResponse = (
  success: boolean,
  data?: any,
  message?: string,
  meta?: any
) => {
  const response: any = {
    success,
    timestamp: new Date().toISOString()
  }

  if (data !== undefined) {
    response.data = data
  }

  if (message) {
    response.message = message
  }

  if (meta) {
    response.meta = meta
  }

  return response
}

/**
 * Handle uncaught exceptions and unhandled rejections
 */
export const setupGlobalErrorHandlers = () => {
  process.on('uncaughtException', (error: Error) => {
    console.error('Uncaught Exception:', error)
    // Log to external service in production
    process.exit(1)
  })

  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
    // Log to external service in production
    // Don't exit process for unhandled rejections in production
    if (process.env.NODE_ENV === 'development') {
      process.exit(1)
    }
  })
}
