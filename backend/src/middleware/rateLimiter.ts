import rateLimit from 'express-rate-limit'
import { Request, Response } from 'express'

/**
 * General API rate limiter
 * 100 requests per 15 minutes per IP
 */
export const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP',
    retryAfter: '15 minutes'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req: Request) => {
    // Use wallet address if authenticated, otherwise IP
    return req.user?.walletAddress || req.ip
  }
})

/**
 * Strict rate limiter for sensitive operations
 * 10 requests per 15 minutes per wallet/IP
 */
export const strictRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each wallet/IP to 10 requests per windowMs
  message: {
    error: 'Too many sensitive requests',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.walletAddress || req.ip
  }
})

/**
 * Trading-specific rate limiter
 * 50 trading requests per 5 minutes per wallet
 */
export const tradingRateLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // limit each wallet to 50 trading requests per windowMs
  message: {
    error: 'Too many trading requests',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.walletAddress || req.ip
  },
  skip: (req: Request) => {
    // Skip rate limiting for GET requests (viewing offers)
    return req.method === 'GET'
  }
})

/**
 * Oracle submission rate limiter
 * 1000 readings per hour per meter
 */
export const oracleRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // limit each meter to 1000 readings per hour
  message: {
    error: 'Too many oracle submissions',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    // Use meter ID for oracle submissions
    const meterId = req.body?.meterId || req.params?.meterId
    return meterId ? `meter:${meterId}` : req.ip
  }
})

/**
 * Authentication rate limiter
 * 5 login attempts per 15 minutes per IP
 */
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 login attempts per windowMs
  message: {
    error: 'Too many authentication attempts',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true // Don't count successful requests
})

/**
 * Create a custom rate limiter with specific configuration
 */
export const createCustomRateLimiter = (options: {
  windowMs: number
  max: number
  message?: string
  keyGenerator?: (req: Request) => string
  skip?: (req: Request) => boolean
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: {
      error: options.message || 'Rate limit exceeded',
      retryAfter: `${Math.ceil(options.windowMs / 60000)} minutes`
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: options.keyGenerator || ((req: Request) => req.ip),
    skip: options.skip
  })
}

/**
 * Dynamic rate limiter based on user tier/subscription
 */
export const dynamicRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: (req: Request) => {
    // Default limits
    let maxRequests = 100

    if (req.user?.walletAddress) {
      // TODO: Implement user tier checking
      // For now, authenticated users get higher limits
      maxRequests = 200
    }

    return maxRequests
  },
  message: {
    error: 'Rate limit exceeded',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.walletAddress || req.ip
  }
})

/**
 * Rate limiter for public endpoints (no authentication required)
 */
export const publicRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for public endpoints
  message: {
    error: 'Too many requests to public API',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
})

/**
 * Rate limiter specifically for WebSocket connections
 */
export const websocketRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 1 connection per second
  message: {
    error: 'Too many WebSocket connection attempts',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false
})
