import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { AuthService } from '../services/authService'

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        walletAddress: string
        sessionId: string
        issuedAt: number
        expiresAt: number
      }
    }
  }
}

const authService = new AuthService()

/**
 * Middleware to authenticate wallet-based requests
 * Supports both JWT tokens and signature-based authentication
 */
export const authenticateWallet = async (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    const walletAddress = req.headers['x-wallet-address'] as string
    const signature = req.headers['x-wallet-signature'] as string
    const timestamp = req.headers['x-timestamp'] as string
    const nonce = req.headers['x-nonce'] as string

    // Method 1: JWT Token Authentication
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret') as any
        
        // Verify session is still valid
        const isValidSession = await authService.validateSession(decoded.sessionId)
        if (!isValidSession) {
          return res.status(401).json({ error: 'Session expired or invalid' })
        }

        req.user = {
          walletAddress: decoded.walletAddress,
          sessionId: decoded.sessionId,
          issuedAt: decoded.iat,
          expiresAt: decoded.exp
        }
        
        return next()
      } catch (jwtError) {
        return res.status(401).json({ error: 'Invalid or expired token' })
      }
    }

    // Method 2: Signature-based Authentication
    if (walletAddress && signature && timestamp && nonce) {
      // Verify timestamp is recent (within 5 minutes)
      const now = Date.now()
      const requestTime = parseInt(timestamp)
      const timeDiff = Math.abs(now - requestTime)
      
      if (timeDiff > 5 * 60 * 1000) { // 5 minutes
        return res.status(401).json({ error: 'Request timestamp too old' })
      }

      // Check nonce hasn't been used recently (prevent replay attacks)
      const isNonceUsed = await authService.checkNonce(nonce, walletAddress)
      if (isNonceUsed) {
        return res.status(401).json({ error: 'Nonce already used' })
      }

      // Create message to verify
      const message = `${walletAddress}:${timestamp}:${nonce}:${req.method}:${req.path}`
      
      // Verify signature
      const isValidSignature = await authService.verifyWalletSignature(
        walletAddress,
        message,
        signature
      )

      if (!isValidSignature) {
        return res.status(401).json({ error: 'Invalid wallet signature' })
      }

      // Store nonce to prevent replay
      await authService.storeNonce(nonce, walletAddress)

      req.user = {
        walletAddress,
        sessionId: `sig_${nonce}`,
        issuedAt: requestTime,
        expiresAt: requestTime + (5 * 60 * 1000) // 5 minutes
      }

      return next()
    }

    // No valid authentication method provided
    return res.status(401).json({ 
      error: 'Authentication required',
      details: 'Provide either Bearer token or wallet signature headers'
    })

  } catch (error) {
    console.error('Authentication error:', error)
    return res.status(500).json({ error: 'Authentication service error' })
  }
}

/**
 * Middleware to authenticate admin/oracle validator requests
 */
export const authenticateValidator = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    const validatorAddress = req.headers['x-validator-address'] as string
    const signature = req.headers['x-validator-signature'] as string

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Bearer token required for validator authentication' })
    }

    const token = authHeader.substring(7)
    
    try {
      const decoded = jwt.verify(token, process.env.VALIDATOR_JWT_SECRET || 'validator-secret') as any
      
      // Verify this is a registered validator
      const isValidValidator = await authService.validateValidator(decoded.validatorAddress)
      if (!isValidValidator) {
        return res.status(403).json({ error: 'Not authorized as validator' })
      }

      req.user = {
        walletAddress: decoded.validatorAddress,
        sessionId: decoded.sessionId,
        issuedAt: decoded.iat,
        expiresAt: decoded.exp
      }

      return next()
    } catch (jwtError) {
      return res.status(401).json({ error: 'Invalid validator token' })
    }

  } catch (error) {
    console.error('Validator authentication error:', error)
    return res.status(500).json({ error: 'Validator authentication service error' })
  }
}

/**
 * Middleware to check if user owns a specific resource
 */
export const authorizeResourceOwner = (resourceParam: string = 'id') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' })
      }

      const resourceId = req.params[resourceParam]
      if (!resourceId) {
        return res.status(400).json({ error: `Resource ${resourceParam} not provided` })
      }

      // Check if user owns this resource
      const isOwner = await authService.checkResourceOwnership(
        req.user.walletAddress,
        resourceId,
        req.route.path
      )

      if (!isOwner) {
        return res.status(403).json({ error: 'Access denied: not resource owner' })
      }

      next()
    } catch (error) {
      console.error('Authorization error:', error)
      return res.status(500).json({ error: 'Authorization service error' })
    }
  }
}

/**
 * Middleware to rate limit based on wallet address
 */
export const walletRateLimit = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  const requestCounts = new Map<string, { count: number; resetTime: number }>()

  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const walletAddress = req.user.walletAddress
    const now = Date.now()
    
    const userRequests = requestCounts.get(walletAddress)
    
    if (!userRequests || now > userRequests.resetTime) {
      // Reset or initialize counter
      requestCounts.set(walletAddress, {
        count: 1,
        resetTime: now + windowMs
      })
      return next()
    }

    if (userRequests.count >= maxRequests) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded',
        resetTime: userRequests.resetTime
      })
    }

    userRequests.count++
    next()
  }
}

/**
 * Generate a JWT token for authenticated wallet
 */
export const generateWalletToken = async (walletAddress: string): Promise<string> => {
  const sessionId = crypto.randomUUID()
  
  // Store session in database/cache
  await authService.createSession(walletAddress, sessionId)

  const payload = {
    walletAddress,
    sessionId,
    type: 'wallet'
  }

  return jwt.sign(payload, process.env.JWT_SECRET || 'default-secret', {
    expiresIn: '24h'
  })
}

/**
 * Generate a JWT token for validator
 */
export const generateValidatorToken = async (validatorAddress: string): Promise<string> => {
  const sessionId = crypto.randomUUID()
  
  const payload = {
    validatorAddress,
    sessionId,
    type: 'validator'
  }

  return jwt.sign(payload, process.env.VALIDATOR_JWT_SECRET || 'validator-secret', {
    expiresIn: '7d'
  })
}
