import { Request, Response, NextFunction } from 'express'
import { getCacheService } from '../services/cacheService'

interface PerformanceMetrics {
  endpoint: string
  method: string
  responseTime: number
  statusCode: number
  timestamp: Date
  userAgent?: string
  ip?: string
  memoryUsage?: NodeJS.MemoryUsage
  cpuUsage?: NodeJS.CpuUsage
}

interface RequestTiming {
  startTime: [number, number]
  startCpuUsage: NodeJS.CpuUsage
}

declare global {
  namespace Express {
    interface Request {
      timing?: RequestTiming
    }
  }
}

class PerformanceMonitor {
  private cacheService = getCacheService()
  private metrics: PerformanceMetrics[] = []
  private maxMetricsInMemory = 1000

  /**
   * Middleware to track request performance
   */
  trackPerformance() {
    return (req: Request, res: Response, next: NextFunction) => {
      // Start timing
      const startTime = process.hrtime()
      const startCpuUsage = process.cpuUsage()
      
      req.timing = {
        startTime,
        startCpuUsage
      }

      // Override res.end to capture metrics
      const originalEnd = res.end
      res.end = function(chunk?: any, encoding?: any) {
        // Calculate response time
        const diff = process.hrtime(startTime)
        const responseTime = diff[0] * 1000 + diff[1] * 1e-6 // Convert to milliseconds

        // Calculate CPU usage
        const cpuUsage = process.cpuUsage(startCpuUsage)

        // Create metrics object
        const metrics: PerformanceMetrics = {
          endpoint: req.route?.path || req.path,
          method: req.method,
          responseTime,
          statusCode: res.statusCode,
          timestamp: new Date(),
          userAgent: req.get('User-Agent'),
          ip: req.ip || req.connection.remoteAddress,
          memoryUsage: process.memoryUsage(),
          cpuUsage
        }

        // Store metrics
        performanceMonitor.recordMetrics(metrics)

        // Call original end
        originalEnd.call(this, chunk, encoding)
      }

      next()
    }
  }

  /**
   * Record performance metrics
   */
  private recordMetrics(metrics: PerformanceMetrics) {
    // Add to in-memory store
    this.metrics.push(metrics)
    
    // Keep only recent metrics in memory
    if (this.metrics.length > this.maxMetricsInMemory) {
      this.metrics = this.metrics.slice(-this.maxMetricsInMemory)
    }

    // Cache recent metrics
    this.cacheRecentMetrics(metrics)

    // Log slow requests
    if (metrics.responseTime > 1000) {
      console.warn(`🐌 Slow request detected: ${metrics.method} ${metrics.endpoint} - ${metrics.responseTime.toFixed(2)}ms`)
    }

    // Log errors
    if (metrics.statusCode >= 400) {
      console.error(`❌ Error response: ${metrics.method} ${metrics.endpoint} - ${metrics.statusCode}`)
    }
  }

  /**
   * Cache recent metrics for quick access
   */
  private async cacheRecentMetrics(metrics: PerformanceMetrics) {
    try {
      // Store in Redis list for recent metrics
      const key = 'performance:recent'
      await this.cacheService.lpush(key, JSON.stringify(metrics))
      
      // Keep only last 100 metrics
      await this.cacheService.lrange(key, 0, 99)
      
      // Update endpoint-specific metrics
      const endpointKey = `performance:endpoint:${metrics.method}:${metrics.endpoint}`
      await this.cacheService.lpush(endpointKey, JSON.stringify({
        responseTime: metrics.responseTime,
        statusCode: metrics.statusCode,
        timestamp: metrics.timestamp
      }))
      
      // Keep only last 50 metrics per endpoint
      await this.cacheService.lrange(endpointKey, 0, 49)
      
    } catch (error) {
      console.error('Error caching performance metrics:', error)
    }
  }

  /**
   * Get performance statistics
   */
  async getStats(timeframe: '1h' | '24h' | '7d' = '1h'): Promise<any> {
    const now = new Date()
    const timeframeDuration = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000
    }

    const cutoff = new Date(now.getTime() - timeframeDuration[timeframe])
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoff)

    if (recentMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        errorRate: 0,
        slowRequests: 0,
        endpointStats: {},
        systemStats: this.getSystemStats()
      }
    }

    // Calculate statistics
    const totalRequests = recentMetrics.length
    const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests
    const errorRequests = recentMetrics.filter(m => m.statusCode >= 400).length
    const errorRate = (errorRequests / totalRequests) * 100
    const slowRequests = recentMetrics.filter(m => m.responseTime > 1000).length

    // Group by endpoint
    const endpointStats: Record<string, any> = {}
    recentMetrics.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`
      if (!endpointStats[key]) {
        endpointStats[key] = {
          count: 0,
          totalResponseTime: 0,
          errors: 0,
          maxResponseTime: 0,
          minResponseTime: Infinity
        }
      }
      
      const stats = endpointStats[key]
      stats.count++
      stats.totalResponseTime += metric.responseTime
      stats.maxResponseTime = Math.max(stats.maxResponseTime, metric.responseTime)
      stats.minResponseTime = Math.min(stats.minResponseTime, metric.responseTime)
      
      if (metric.statusCode >= 400) {
        stats.errors++
      }
    })

    // Calculate averages for endpoints
    Object.keys(endpointStats).forEach(key => {
      const stats = endpointStats[key]
      stats.averageResponseTime = stats.totalResponseTime / stats.count
      stats.errorRate = (stats.errors / stats.count) * 100
      delete stats.totalResponseTime
    })

    return {
      totalRequests,
      averageResponseTime: Math.round(averageResponseTime * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      slowRequests,
      endpointStats,
      systemStats: this.getSystemStats(),
      timeframe
    }
  }

  /**
   * Get system performance statistics
   */
  private getSystemStats() {
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    return {
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: Math.round(process.uptime()),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    }
  }

  /**
   * Get endpoint-specific performance data
   */
  async getEndpointStats(method: string, endpoint: string): Promise<any> {
    try {
      const key = `performance:endpoint:${method}:${endpoint}`
      const metricsData = await this.cacheService.lrange(key, 0, -1)
      
      const metrics = metricsData.map(data => JSON.parse(data))
      
      if (metrics.length === 0) {
        return null
      }

      const responseTimes = metrics.map(m => m.responseTime)
      const statusCodes = metrics.map(m => m.statusCode)
      
      return {
        totalRequests: metrics.length,
        averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        minResponseTime: Math.min(...responseTimes),
        maxResponseTime: Math.max(...responseTimes),
        p95ResponseTime: this.calculatePercentile(responseTimes, 95),
        p99ResponseTime: this.calculatePercentile(responseTimes, 99),
        errorRate: (statusCodes.filter(code => code >= 400).length / statusCodes.length) * 100,
        recentMetrics: metrics.slice(0, 10) // Last 10 requests
      }
    } catch (error) {
      console.error('Error getting endpoint stats:', error)
      return null
    }
  }

  /**
   * Calculate percentile
   */
  private calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[index] || 0
  }

  /**
   * Clear old metrics
   */
  clearOldMetrics(olderThan: Date = new Date(Date.now() - 24 * 60 * 60 * 1000)) {
    this.metrics = this.metrics.filter(m => m.timestamp >= olderThan)
  }

  /**
   * Health check for performance monitoring
   */
  async healthCheck(): Promise<boolean> {
    try {
      const stats = this.getSystemStats()
      
      // Check memory usage (alert if over 80%)
      const memoryUsagePercent = (stats.memory.heapUsed / stats.memory.heapTotal) * 100
      if (memoryUsagePercent > 80) {
        console.warn(`⚠️ High memory usage: ${memoryUsagePercent.toFixed(1)}%`)
      }

      // Check if cache service is healthy
      const cacheHealthy = await this.cacheService.healthCheck()
      if (!cacheHealthy) {
        console.warn('⚠️ Cache service is not healthy')
      }

      return true
    } catch (error) {
      console.error('Performance monitor health check failed:', error)
      return false
    }
  }
}

// Singleton instance
const performanceMonitor = new PerformanceMonitor()

export { performanceMonitor, PerformanceMonitor }
export default performanceMonitor.trackPerformance()
