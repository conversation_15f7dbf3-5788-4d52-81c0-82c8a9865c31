import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import TradingDashboard from '../../components/TradingDashboard'
import { useEnergyStore } from '../../store/energyStore'

// Mock the energy store
jest.mock('../../store/energyStore')
const mockUseEnergyStore = useEnergyStore as jest.MockedFunction<typeof useEnergyStore>

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  success: jest.fn(),
  error: jest.fn(),
  loading: jest.fn(),
}))

describe('TradingDashboard', () => {
  const mockStoreState = {
    marketData: {
      totalOffers: 25,
      totalVolumeTraded: 1500,
      averagePrice: 0.14,
      priceChange24h: 5.2,
      volume24h: 300,
      activeBuyers: 12,
      activeSellers: 8
    },
    userOffers: [
      {
        id: 'offer-1',
        seller: 'user-wallet',
        energyAmount: 100,
        pricePerKwh: 0.15,
        offerType: 'immediate',
        status: 'active',
        createdAt: new Date('2024-01-15T10:00:00Z'),
        expiresAt: new Date('2024-01-16T10:00:00Z'),
        filledAmount: 0
      },
      {
        id: 'offer-2',
        seller: 'user-wallet',
        energyAmount: 50,
        pricePerKwh: 0.12,
        offerType: 'scheduled',
        status: 'completed',
        createdAt: new Date('2024-01-14T10:00:00Z'),
        expiresAt: new Date('2024-01-15T10:00:00Z'),
        filledAmount: 50
      }
    ],
    isLoading: false,
    createOffer: jest.fn(),
    cancelOffer: jest.fn(),
    executeTrade: jest.fn(),
    fetchUserOffers: jest.fn(),
    fetchMarketData: jest.fn()
  }

  beforeEach(() => {
    mockUseEnergyStore.mockReturnValue(mockStoreState)
    jest.clearAllMocks()
  })

  describe('Rendering', () => {
    test('renders trading dashboard with market data', () => {
      render(<TradingDashboard />)

      expect(screen.getByText('Energy Trading Dashboard')).toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument() // Total offers
      expect(screen.getByText('1,500 kWh')).toBeInTheDocument() // Total volume
      expect(screen.getByText('$0.14')).toBeInTheDocument() // Average price
    })

    test('renders user offers section', () => {
      render(<TradingDashboard />)

      expect(screen.getByText('Your Offers')).toBeInTheDocument()
      expect(screen.getByText('100 kWh')).toBeInTheDocument()
      expect(screen.getByText('$0.15/kWh')).toBeInTheDocument()
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getByText('Completed')).toBeInTheDocument()
    })

    test('renders create offer button', () => {
      render(<TradingDashboard />)

      const createButton = screen.getByText('Create New Offer')
      expect(createButton).toBeInTheDocument()
    })

    test('shows loading state when isLoading is true', () => {
      mockUseEnergyStore.mockReturnValue({
        ...mockStoreState,
        isLoading: true
      })

      render(<TradingDashboard />)

      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })
  })

  describe('Create Offer Modal', () => {
    test('opens create offer modal when button is clicked', () => {
      render(<TradingDashboard />)

      const createButton = screen.getByText('Create New Offer')
      fireEvent.click(createButton)

      expect(screen.getByText('Create Energy Offer')).toBeInTheDocument()
      expect(screen.getByLabelText('Energy Amount (kWh)')).toBeInTheDocument()
      expect(screen.getByLabelText('Price per kWh ($)')).toBeInTheDocument()
    })

    test('closes modal when cancel button is clicked', () => {
      render(<TradingDashboard />)

      // Open modal
      const createButton = screen.getByText('Create New Offer')
      fireEvent.click(createButton)

      // Close modal
      const cancelButton = screen.getByText('Cancel')
      fireEvent.click(cancelButton)

      expect(screen.queryByText('Create Energy Offer')).not.toBeInTheDocument()
    })

    test('validates form inputs', async () => {
      render(<TradingDashboard />)

      // Open modal
      const createButton = screen.getByText('Create New Offer')
      fireEvent.click(createButton)

      // Try to submit without filling required fields
      const submitButton = screen.getByText('Create Offer')
      fireEvent.click(submitButton)

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText('Energy amount is required')).toBeInTheDocument()
        expect(screen.getByText('Price is required')).toBeInTheDocument()
      })
    })

    test('submits form with valid data', async () => {
      render(<TradingDashboard />)

      // Open modal
      const createButton = screen.getByText('Create New Offer')
      fireEvent.click(createButton)

      // Fill form
      const energyAmountInput = screen.getByLabelText('Energy Amount (kWh)')
      const priceInput = screen.getByLabelText('Price per kWh ($)')
      const durationInput = screen.getByLabelText('Duration (hours)')

      fireEvent.change(energyAmountInput, { target: { value: '150' } })
      fireEvent.change(priceInput, { target: { value: '0.16' } })
      fireEvent.change(durationInput, { target: { value: '48' } })

      // Submit form
      const submitButton = screen.getByText('Create Offer')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockStoreState.createOffer).toHaveBeenCalledWith({
          energyAmount: 150,
          pricePerKwh: 0.16,
          offerType: 'immediate',
          duration: 48
        })
      })
    })
  })

  describe('Offer Management', () => {
    test('cancels offer when cancel button is clicked', async () => {
      render(<TradingDashboard />)

      const cancelButtons = screen.getAllByText('Cancel')
      const offerCancelButton = cancelButtons.find(button => 
        button.closest('[data-testid="offer-item"]')
      )

      if (offerCancelButton) {
        fireEvent.click(offerCancelButton)

        await waitFor(() => {
          expect(mockStoreState.cancelOffer).toHaveBeenCalledWith('offer-1')
        })
      }
    })

    test('filters offers by status', () => {
      render(<TradingDashboard />)

      // Check that both active and completed offers are shown initially
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getByText('Completed')).toBeInTheDocument()

      // Click on active filter
      const activeFilter = screen.getByText('Active (1)')
      fireEvent.click(activeFilter)

      // Should only show active offers
      expect(screen.getByText('100 kWh')).toBeInTheDocument() // Active offer
      expect(screen.queryByText('50 kWh')).not.toBeInTheDocument() // Completed offer should be hidden
    })
  })

  describe('Market Statistics', () => {
    test('displays market statistics correctly', () => {
      render(<TradingDashboard />)

      expect(screen.getByText('25')).toBeInTheDocument() // Total offers
      expect(screen.getByText('1,500 kWh')).toBeInTheDocument() // Volume traded
      expect(screen.getByText('$0.14')).toBeInTheDocument() // Average price
      expect(screen.getByText('+5.2%')).toBeInTheDocument() // Price change
    })

    test('handles negative price change', () => {
      mockUseEnergyStore.mockReturnValue({
        ...mockStoreState,
        marketData: {
          ...mockStoreState.marketData,
          priceChange24h: -3.1
        }
      })

      render(<TradingDashboard />)

      expect(screen.getByText('-3.1%')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    test('handles create offer error', async () => {
      const mockCreateOfferError = jest.fn().mockRejectedValue(new Error('Failed to create offer'))
      mockUseEnergyStore.mockReturnValue({
        ...mockStoreState,
        createOffer: mockCreateOfferError
      })

      render(<TradingDashboard />)

      // Open modal and submit form
      const createButton = screen.getByText('Create New Offer')
      fireEvent.click(createButton)

      const energyAmountInput = screen.getByLabelText('Energy Amount (kWh)')
      const priceInput = screen.getByLabelText('Price per kWh ($)')

      fireEvent.change(energyAmountInput, { target: { value: '100' } })
      fireEvent.change(priceInput, { target: { value: '0.15' } })

      const submitButton = screen.getByText('Create Offer')
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockCreateOfferError).toHaveBeenCalled()
      })
    })

    test('handles empty offers list', () => {
      mockUseEnergyStore.mockReturnValue({
        ...mockStoreState,
        userOffers: []
      })

      render(<TradingDashboard />)

      expect(screen.getByText('No offers found')).toBeInTheDocument()
      expect(screen.getByText('Create your first energy offer to start trading')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(<TradingDashboard />)

      expect(screen.getByLabelText('Energy Amount (kWh)')).toBeInTheDocument()
      expect(screen.getByLabelText('Price per kWh ($)')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Create New Offer' })).toBeInTheDocument()
    })

    test('supports keyboard navigation', () => {
      render(<TradingDashboard />)

      const createButton = screen.getByText('Create New Offer')
      createButton.focus()
      expect(createButton).toHaveFocus()

      // Simulate Enter key press
      fireEvent.keyDown(createButton, { key: 'Enter', code: 'Enter' })
      expect(screen.getByText('Create Energy Offer')).toBeInTheDocument()
    })
  })

  describe('Real-time Updates', () => {
    test('refreshes data when component mounts', () => {
      render(<TradingDashboard />)

      expect(mockStoreState.fetchMarketData).toHaveBeenCalled()
      expect(mockStoreState.fetchUserOffers).toHaveBeenCalled()
    })

    test('updates when market data changes', () => {
      const { rerender } = render(<TradingDashboard />)

      // Update market data
      mockUseEnergyStore.mockReturnValue({
        ...mockStoreState,
        marketData: {
          ...mockStoreState.marketData,
          totalOffers: 30,
          averagePrice: 0.16
        }
      })

      rerender(<TradingDashboard />)

      expect(screen.getByText('30')).toBeInTheDocument()
      expect(screen.getByText('$0.16')).toBeInTheDocument()
    })
  })
})
