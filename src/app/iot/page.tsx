'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import IoTDashboard from '@/components/iot/IoTDashboard'
import { useWallet } from '@/components/providers/WalletProvider'
import { Smartphone, Wifi, Activity, Zap, Sun, Battery, Car } from 'lucide-react'

export default function IoTPage() {
  const { connected, connect } = useWallet()

  if (!connected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Smartphone className="h-6 w-6" />
              IoT Device Management
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Connect your MasChain wallet to manage IoT devices and energy readings
            </p>
            <button
              onClick={connect}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
            >
              Connect Wallet
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Smartphone className="h-8 w-8 text-green-600" />
            <h1 className="text-3xl font-bold text-gray-900">IoT Device Management</h1>
            <Badge className="bg-blue-100 text-blue-800">
              Phase 2 - Real-time Data
            </Badge>
          </div>
          <p className="text-gray-600">
            Monitor and manage your energy devices with real-time data collection and validation
          </p>
        </div>

        {/* IoT Dashboard */}
        <IoTDashboard />

        {/* Feature Information */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Supported Device Types</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-3">
                  <Activity className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-semibold">Smart Meters</h3>
                    <p className="text-sm text-gray-500">Energy monitoring</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Real-time consumption tracking</li>
                  <li>• Grid import/export monitoring</li>
                  <li>• Automated billing integration</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-3">
                  <Sun className="h-8 w-8 text-yellow-600" />
                  <div>
                    <h3 className="font-semibold">Solar Panels</h3>
                    <p className="text-sm text-gray-500">Production tracking</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Solar energy production</li>
                  <li>• Weather correlation</li>
                  <li>• Performance optimization</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-3">
                  <Battery className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-semibold">Battery Storage</h3>
                    <p className="text-sm text-gray-500">Energy storage</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Charge/discharge cycles</li>
                  <li>• State of charge monitoring</li>
                  <li>• Capacity optimization</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-3">
                  <Car className="h-8 w-8 text-purple-600" />
                  <div>
                    <h3 className="font-semibold">EV Chargers</h3>
                    <p className="text-sm text-gray-500">Vehicle charging</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Charging session tracking</li>
                  <li>• Smart charging schedules</li>
                  <li>• Grid load balancing</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Technical Features */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Technical Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wifi className="h-5 w-5" />
                  MQTT Integration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Real-time data streaming
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Secure device authentication
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Automatic reconnection
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Message queuing & reliability
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Data Validation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Cryptographic signatures
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Oracle network verification
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Anomaly detection
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Blockchain immutability
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Getting Started */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started with IoT Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-blue-600">1</span>
                  </div>
                  <h3 className="font-semibold mb-2">Register Device</h3>
                  <p className="text-sm text-gray-600">
                    Add your energy device to the platform with location and specifications
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-green-600">2</span>
                  </div>
                  <h3 className="font-semibold mb-2">Connect & Verify</h3>
                  <p className="text-sm text-gray-600">
                    Establish secure connection and complete device verification process
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-purple-600">3</span>
                  </div>
                  <h3 className="font-semibold mb-2">Start Trading</h3>
                  <p className="text-sm text-gray-600">
                    Begin submitting readings and trading energy based on real data
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
