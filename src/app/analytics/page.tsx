'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard'
import { useWallet } from '@/components/providers/WalletProvider'
import { BarChart3, TrendingUp, Leaf, Target, Brain, Zap } from 'lucide-react'

export default function AnalyticsPage() {
  const { connected, connect } = useWallet()

  if (!connected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <BarChart3 className="h-6 w-6" />
              Energy Analytics
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Connect your <PERSON>s<PERSON>hain wallet to access advanced analytics and insights
            </p>
            <button
              onClick={connect}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Connect Wallet
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-900">Energy Analytics</h1>
            <Badge className="bg-purple-100 text-purple-800">
              Phase 2 - AI Insights
            </Badge>
          </div>
          <p className="text-gray-600">
            Advanced analytics, forecasting, and performance insights for your energy trading
          </p>
        </div>

        {/* Analytics Dashboard */}
        <AnalyticsDashboard />

        {/* Feature Overview */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Analytics Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-semibold">Performance Metrics</h3>
                    <p className="text-sm text-gray-500">Real-time tracking</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Energy efficiency analysis</li>
                  <li>• Trading performance metrics</li>
                  <li>• Profitability tracking</li>
                  <li>• Benchmark comparisons</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Leaf className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-semibold">Carbon Footprint</h3>
                    <p className="text-sm text-gray-500">Environmental impact</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• CO₂ emissions tracking</li>
                  <li>• Carbon offset calculations</li>
                  <li>• Renewable energy percentage</li>
                  <li>• Environmental equivalents</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Brain className="h-8 w-8 text-purple-600" />
                  <div>
                    <h3 className="font-semibold">AI Forecasting</h3>
                    <p className="text-sm text-gray-500">Predictive analytics</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Energy production forecasts</li>
                  <li>• Consumption predictions</li>
                  <li>• Price trend analysis</li>
                  <li>• Demand forecasting</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Target className="h-8 w-8 text-orange-600" />
                  <div>
                    <h3 className="font-semibold">Smart Alerts</h3>
                    <p className="text-sm text-gray-500">Automated notifications</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Price threshold alerts</li>
                  <li>• Efficiency drop warnings</li>
                  <li>• Market opportunity notifications</li>
                  <li>• Device status alerts</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Zap className="h-8 w-8 text-yellow-600" />
                  <div>
                    <h3 className="font-semibold">Market Insights</h3>
                    <p className="text-sm text-gray-500">Trading intelligence</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Market trend analysis</li>
                  <li>• Volume spike detection</li>
                  <li>• Supply/demand insights</li>
                  <li>• Competitive analysis</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <BarChart3 className="h-8 w-8 text-indigo-600" />
                  <div>
                    <h3 className="font-semibold">Custom Reports</h3>
                    <p className="text-sm text-gray-500">Detailed analysis</p>
                  </div>
                </div>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li>• Transaction history reports</li>
                  <li>• Performance summaries</li>
                  <li>• Tax reporting assistance</li>
                  <li>• Data export capabilities</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* How It Works */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>How Analytics Work</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-blue-600">1</span>
                  </div>
                  <h3 className="font-semibold mb-2">Data Collection</h3>
                  <p className="text-sm text-gray-600">
                    Gather real-time data from IoT devices, trading activities, and market conditions
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-green-600">2</span>
                  </div>
                  <h3 className="font-semibold mb-2">AI Processing</h3>
                  <p className="text-sm text-gray-600">
                    Apply machine learning algorithms to identify patterns and generate insights
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-purple-600">3</span>
                  </div>
                  <h3 className="font-semibold mb-2">Insight Generation</h3>
                  <p className="text-sm text-gray-600">
                    Create actionable insights, forecasts, and recommendations for optimization
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl font-bold text-orange-600">4</span>
                  </div>
                  <h3 className="font-semibold mb-2">Action & Alerts</h3>
                  <p className="text-sm text-gray-600">
                    Deliver personalized recommendations and automated alerts for optimal trading
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Benefits */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Benefits of Advanced Analytics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">For Energy Producers</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Optimize energy production timing
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Maximize revenue from energy sales
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Predict maintenance needs
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Track environmental impact
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">For Energy Consumers</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Reduce energy costs through smart buying
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Optimize consumption patterns
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Identify energy-saving opportunities
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Monitor carbon footprint reduction
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
