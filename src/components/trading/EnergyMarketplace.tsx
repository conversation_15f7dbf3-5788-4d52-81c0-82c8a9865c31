'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Users,
  Filter,
  Search
} from 'lucide-react'

interface EnergyOffer {
  id: string
  seller: string
  energyAmount: number
  pricePerKwh: number
  offerType: 'immediate' | 'scheduled' | 'recurring'
  expiresAt: number
  filledAmount: number
  status: 'active' | 'completed' | 'cancelled'
  location: string
  energySource: 'solar' | 'wind' | 'hydro' | 'mixed'
}

export default function EnergyMarketplace() {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'immediate' | 'scheduled' | 'recurring'>('all')
  const [sortBy, setSortBy] = useState<'price' | 'amount' | 'time'>('price')

  // Mock data - in real app this would come from the store
  const marketOffers: EnergyOffer[] = [
    {
      id: '1',
      seller: 'Solar Farm Alpha',
      energyAmount: 500,
      pricePerKwh: 0.085,
      offerType: 'immediate',
      expiresAt: Date.now() + 3600000,
      filledAmount: 150,
      status: 'active',
      location: 'Kuala Lumpur',
      energySource: 'solar'
    },
    {
      id: '2',
      seller: 'Wind Power Beta',
      energyAmount: 300,
      pricePerKwh: 0.092,
      offerType: 'scheduled',
      expiresAt: Date.now() + 7200000,
      filledAmount: 0,
      status: 'active',
      location: 'Penang',
      energySource: 'wind'
    },
    {
      id: '3',
      seller: 'Hydro Station Gamma',
      energyAmount: 750,
      pricePerKwh: 0.078,
      offerType: 'recurring',
      expiresAt: Date.now() + 86400000,
      filledAmount: 200,
      status: 'active',
      location: 'Sabah',
      energySource: 'hydro'
    }
  ]

  const filteredOffers = marketOffers
    .filter(offer => {
      const matchesSearch = offer.seller.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           offer.location.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesFilter = filterType === 'all' || offer.offerType === filterType
      return matchesSearch && matchesFilter && offer.status === 'active'
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.pricePerKwh - b.pricePerKwh
        case 'amount':
          return (b.energyAmount - b.filledAmount) - (a.energyAmount - a.filledAmount)
        case 'time':
          return a.expiresAt - b.expiresAt
        default:
          return 0
      }
    })

  const formatPrice = (value: number) => `$${value.toFixed(3)}`
  const formatTimeLeft = (expiresAt: number) => {
    const timeLeft = Math.max(0, expiresAt - Date.now())
    const hours = Math.floor(timeLeft / (1000 * 60 * 60))
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
  }

  const getEnergySourceIcon = (source: string) => {
    switch (source) {
      case 'solar': return '☀️'
      case 'wind': return '💨'
      case 'hydro': return '💧'
      default: return '⚡'
    }
  }

  const getOfferTypeColor = (type: string) => {
    switch (type) {
      case 'immediate': return 'bg-green-100 text-green-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'recurring': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Market Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Offers</p>
                <p className="text-2xl font-bold">{marketOffers.filter(o => o.status === 'active').length}</p>
              </div>
              <Zap className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Price</p>
                <p className="text-2xl font-bold">$0.085</p>
              </div>
              <TrendingDown className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Available</p>
                <p className="text-2xl font-bold">1.2k kWh</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Traders</p>
                <p className="text-2xl font-bold">24</p>
              </div>
              <Users className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Market Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by seller or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="immediate">Immediate</option>
              <option value="scheduled">Scheduled</option>
              <option value="recurring">Recurring</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="price">Sort by Price</option>
              <option value="amount">Sort by Amount</option>
              <option value="time">Sort by Time</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Market Offers */}
      <Card>
        <CardHeader>
          <CardTitle>Available Energy Offers</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredOffers.length === 0 ? (
            <div className="text-center py-8">
              <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No offers match your criteria</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredOffers.map((offer) => {
                const available = offer.energyAmount - offer.filledAmount
                const progress = (offer.filledAmount / offer.energyAmount) * 100
                
                return (
                  <div key={offer.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <span className="text-2xl">{getEnergySourceIcon(offer.energySource)}</span>
                          <div>
                            <h3 className="font-semibold text-gray-900">{offer.seller}</h3>
                            <p className="text-sm text-gray-600">{offer.location}</p>
                          </div>
                          <Badge className={getOfferTypeColor(offer.offerType)}>
                            {offer.offerType}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                          <div>
                            <p className="text-sm text-gray-600">Available</p>
                            <p className="font-semibold">{available} kWh</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Price</p>
                            <p className="font-semibold">{formatPrice(offer.pricePerKwh)}/kWh</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Total Cost</p>
                            <p className="font-semibold">{formatPrice(available * offer.pricePerKwh)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Time Left
                            </p>
                            <p className="font-semibold">{formatTimeLeft(offer.expiresAt)}</p>
                          </div>
                        </div>

                        <div className="mb-3">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{offer.filledAmount}/{offer.energyAmount} kWh</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                              style={{ width: `${progress}%` }}
                            />
                          </div>
                        </div>
                      </div>
                      
                      <div className="ml-4 flex flex-col gap-2">
                        <button
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                          disabled={available <= 0}
                        >
                          Buy Energy
                        </button>
                        <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
