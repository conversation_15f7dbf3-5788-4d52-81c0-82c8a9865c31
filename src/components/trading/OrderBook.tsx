'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { TrendingUp, TrendingDown, Activity, Zap } from 'lucide-react'

interface OrderBookEntry {
  id: string
  offerId: string
  seller: string
  energyAmount: number
  remainingAmount: number
  pricePerKwh: number
  orderType: 'buy' | 'sell'
  timestamp: Date
  expiresAt: Date
  status: 'active' | 'filled' | 'cancelled' | 'expired'
}

interface OrderBookSnapshot {
  bids: OrderBookEntry[]
  asks: OrderBookEntry[]
  lastPrice: number
  volume24h: number
  priceChange24h: number
  timestamp: Date
}

interface MarketDepth {
  level: number
  price: number
  amount: number
  total: number
  orders: number
}

export default function OrderBook() {
  const [orderBook, setOrderBook] = useState<OrderBookSnapshot | null>(null)
  const [marketDepth, setMarketDepth] = useState<{ bids: MarketDepth[]; asks: MarketDepth[] } | null>(null)
  const [newOrder, setNewOrder] = useState({
    orderType: 'buy' as 'buy' | 'sell',
    energyAmount: '',
    pricePerKwh: '',
    expiresIn: '24'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [ws, setWs] = useState<WebSocket | null>(null)

  // Initialize WebSocket connection for real-time updates
  useEffect(() => {
    const token = localStorage.getItem('auth_token')
    const wsUrl = `ws://localhost:3001?token=${token}`
    
    const websocket = new WebSocket(wsUrl)
    
    websocket.onopen = () => {
      console.log('📡 WebSocket connected for order book')
      // Subscribe to order book updates
      websocket.send(JSON.stringify({
        type: 'subscribe',
        data: { channels: ['market', 'orderbook'] }
      }))
    }
    
    websocket.onmessage = (event) => {
      const message = JSON.parse(event.data)
      
      if (message.type === 'market_update' && message.updateType === 'orderbook_update') {
        setOrderBook(message.data)
      }
    }
    
    websocket.onclose = () => {
      console.log('📡 WebSocket disconnected')
    }
    
    setWs(websocket)
    
    return () => {
      websocket.close()
    }
  }, [])

  // Fetch initial order book data
  useEffect(() => {
    fetchOrderBook()
    fetchMarketDepth()
  }, [])

  const fetchOrderBook = async () => {
    try {
      const response = await fetch('/api/market/orderbook')
      if (response.ok) {
        const result = await response.json()
        setOrderBook(result.data)
      }
    } catch (error) {
      console.error('Error fetching order book:', error)
    }
  }

  const fetchMarketDepth = async () => {
    try {
      const response = await fetch('/api/market/depth?levels=10')
      if (response.ok) {
        const result = await response.json()
        setMarketDepth(result.data)
      }
    } catch (error) {
      console.error('Error fetching market depth:', error)
    }
  }

  const handlePlaceOrder = async () => {
    if (!newOrder.energyAmount || !newOrder.pricePerKwh) return

    setIsLoading(true)
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/market/orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          energyAmount: parseFloat(newOrder.energyAmount),
          pricePerKwh: parseFloat(newOrder.pricePerKwh),
          orderType: newOrder.orderType,
          expiresIn: parseInt(newOrder.expiresIn)
        })
      })

      if (response.ok) {
        setNewOrder({
          orderType: 'buy',
          energyAmount: '',
          pricePerKwh: '',
          expiresIn: '24'
        })
        // Order book will update via WebSocket
      } else {
        const error = await response.json()
        console.error('Error placing order:', error)
      }
    } catch (error) {
      console.error('Error placing order:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleMarketOrder = async () => {
    if (!newOrder.energyAmount) return

    setIsLoading(true)
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/market/market-order', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          energyAmount: parseFloat(newOrder.energyAmount),
          orderType: newOrder.orderType,
          maxSlippage: 5
        })
      })

      if (response.ok) {
        setNewOrder({
          orderType: 'buy',
          energyAmount: '',
          pricePerKwh: '',
          expiresIn: '24'
        })
      } else {
        const error = await response.json()
        console.error('Error placing market order:', error)
      }
    } catch (error) {
      console.error('Error placing market order:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatPrice = (price: number) => price.toFixed(4)
  const formatAmount = (amount: number) => amount.toFixed(2)

  if (!orderBook) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Order Book
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Market Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Market Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{formatPrice(orderBook.lastPrice)}</div>
              <div className="text-sm text-gray-500">Last Price (MAS/kWh)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{formatAmount(orderBook.volume24h)}</div>
              <div className="text-sm text-gray-500">24h Volume (kWh)</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold flex items-center justify-center gap-1 ${
                orderBook.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {orderBook.priceChange24h >= 0 ? <TrendingUp className="h-5 w-5" /> : <TrendingDown className="h-5 w-5" />}
                {Math.abs(orderBook.priceChange24h).toFixed(2)}%
              </div>
              <div className="text-sm text-gray-500">24h Change</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Book */}
        <Card>
          <CardHeader>
            <CardTitle>Order Book</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="book" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="book">Order Book</TabsTrigger>
                <TabsTrigger value="depth">Market Depth</TabsTrigger>
              </TabsList>
              
              <TabsContent value="book" className="space-y-4">
                {/* Sell Orders (Asks) */}
                <div>
                  <h4 className="text-sm font-medium text-red-600 mb-2">Sell Orders</h4>
                  <div className="space-y-1">
                    {orderBook.asks.slice(0, 10).map((order, index) => (
                      <div key={order.id} className="grid grid-cols-3 gap-2 text-sm py-1 px-2 hover:bg-red-50 rounded">
                        <div className="text-red-600">{formatPrice(order.pricePerKwh)}</div>
                        <div>{formatAmount(order.remainingAmount)}</div>
                        <div className="text-gray-500">{formatAmount(order.pricePerKwh * order.remainingAmount)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Spread */}
                <div className="border-t border-b py-2 text-center">
                  <div className="text-sm text-gray-500">
                    Spread: {orderBook.asks[0] && orderBook.bids[0] ? 
                      formatPrice(orderBook.asks[0].pricePerKwh - orderBook.bids[0].pricePerKwh) : 
                      'N/A'
                    }
                  </div>
                </div>

                {/* Buy Orders (Bids) */}
                <div>
                  <h4 className="text-sm font-medium text-green-600 mb-2">Buy Orders</h4>
                  <div className="space-y-1">
                    {orderBook.bids.slice(0, 10).map((order, index) => (
                      <div key={order.id} className="grid grid-cols-3 gap-2 text-sm py-1 px-2 hover:bg-green-50 rounded">
                        <div className="text-green-600">{formatPrice(order.pricePerKwh)}</div>
                        <div>{formatAmount(order.remainingAmount)}</div>
                        <div className="text-gray-500">{formatAmount(order.pricePerKwh * order.remainingAmount)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-2 text-xs text-gray-500 border-t pt-2">
                  <div>Price (MAS/kWh)</div>
                  <div>Amount (kWh)</div>
                  <div>Total (MAS)</div>
                </div>
              </TabsContent>

              <TabsContent value="depth" className="space-y-4">
                {marketDepth && (
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-green-600 mb-2">Buy Depth</h4>
                      {marketDepth.bids.map((level, index) => (
                        <div key={index} className="grid grid-cols-4 gap-2 text-sm py-1">
                          <div className="text-green-600">{formatPrice(level.price)}</div>
                          <div>{formatAmount(level.amount)}</div>
                          <div>{formatAmount(level.total)}</div>
                          <div className="text-gray-500">{level.orders}</div>
                        </div>
                      ))}
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-red-600 mb-2">Sell Depth</h4>
                      {marketDepth.asks.map((level, index) => (
                        <div key={index} className="grid grid-cols-4 gap-2 text-sm py-1">
                          <div className="text-red-600">{formatPrice(level.price)}</div>
                          <div>{formatAmount(level.amount)}</div>
                          <div>{formatAmount(level.total)}</div>
                          <div className="text-gray-500">{level.orders}</div>
                        </div>
                      ))}
                    </div>

                    <div className="grid grid-cols-4 gap-2 text-xs text-gray-500 border-t pt-2">
                      <div>Price</div>
                      <div>Amount</div>
                      <div>Total</div>
                      <div>Orders</div>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Place Order */}
        <Card>
          <CardHeader>
            <CardTitle>Place Order</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs value={newOrder.orderType} onValueChange={(value) => setNewOrder({...newOrder, orderType: value as 'buy' | 'sell'})}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="buy" className="text-green-600">Buy</TabsTrigger>
                <TabsTrigger value="sell" className="text-red-600">Sell</TabsTrigger>
              </TabsList>
              
              <TabsContent value="buy" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amount">Amount (kWh)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="0.00"
                      value={newOrder.energyAmount}
                      onChange={(e) => setNewOrder({...newOrder, energyAmount: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="price">Price (MAS/kWh)</Label>
                    <Input
                      id="price"
                      type="number"
                      placeholder="0.0000"
                      value={newOrder.pricePerKwh}
                      onChange={(e) => setNewOrder({...newOrder, pricePerKwh: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="expires">Expires In (hours)</Label>
                    <Input
                      id="expires"
                      type="number"
                      value={newOrder.expiresIn}
                      onChange={(e) => setNewOrder({...newOrder, expiresIn: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Button 
                      onClick={handlePlaceOrder} 
                      disabled={isLoading || !newOrder.energyAmount || !newOrder.pricePerKwh}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      {isLoading ? 'Placing...' : 'Place Buy Order'}
                    </Button>
                    
                    <Button 
                      onClick={handleMarketOrder} 
                      disabled={isLoading || !newOrder.energyAmount}
                      variant="outline"
                      className="w-full"
                    >
                      Market Buy
                    </Button>
                  </div>
                  
                  {newOrder.energyAmount && newOrder.pricePerKwh && (
                    <div className="text-sm text-gray-500 border-t pt-2">
                      Total: {(parseFloat(newOrder.energyAmount) * parseFloat(newOrder.pricePerKwh)).toFixed(4)} MAS
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="sell" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amount">Amount (kWh)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="0.00"
                      value={newOrder.energyAmount}
                      onChange={(e) => setNewOrder({...newOrder, energyAmount: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="price">Price (MAS/kWh)</Label>
                    <Input
                      id="price"
                      type="number"
                      placeholder="0.0000"
                      value={newOrder.pricePerKwh}
                      onChange={(e) => setNewOrder({...newOrder, pricePerKwh: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="expires">Expires In (hours)</Label>
                    <Input
                      id="expires"
                      type="number"
                      value={newOrder.expiresIn}
                      onChange={(e) => setNewOrder({...newOrder, expiresIn: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Button 
                      onClick={handlePlaceOrder} 
                      disabled={isLoading || !newOrder.energyAmount || !newOrder.pricePerKwh}
                      className="w-full bg-red-600 hover:bg-red-700"
                    >
                      {isLoading ? 'Placing...' : 'Place Sell Order'}
                    </Button>
                    
                    <Button 
                      onClick={handleMarketOrder} 
                      disabled={isLoading || !newOrder.energyAmount}
                      variant="outline"
                      className="w-full"
                    >
                      Market Sell
                    </Button>
                  </div>
                  
                  {newOrder.energyAmount && newOrder.pricePerKwh && (
                    <div className="text-sm text-gray-500 border-t pt-2">
                      Total: {(parseFloat(newOrder.energyAmount) * parseFloat(newOrder.pricePerKwh)).toFixed(4)} MAS
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
