'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Smartphone, 
  Wifi, 
  WifiOff, 
  AlertTriangle, 
  CheckCircle, 
  Plus,
  Activity,
  Battery,
  Zap,
  Sun,
  Car
} from 'lucide-react'

interface IoTDevice {
  deviceId: string
  deviceType: 'smart_meter' | 'solar_panel' | 'battery' | 'ev_charger'
  connectionStatus: 'online' | 'offline' | 'error'
  lastSeen: Date
  isVerified: boolean
}

interface IoTReading {
  id: string
  deviceId: string
  timestamp: Date
  readingType: 'production' | 'consumption' | 'battery_level' | 'grid_export' | 'grid_import'
  value: number
  unit: 'kWh' | 'kW' | 'V' | 'A' | '%'
  quality: 'good' | 'fair' | 'poor'
  verified: boolean
}

interface DeviceAlert {
  id: string
  deviceId: string
  alertType: 'offline' | 'anomaly' | 'maintenance' | 'error'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: Date
  acknowledged: boolean
}

interface DashboardData {
  summary: {
    totalDevices: number
    onlineDevices: number
    verifiedDevices: number
    totalAlerts: number
  }
  devices: Array<{
    device: IoTDevice
    recentReadings: IoTReading[]
    unacknowledgedAlerts: number
    totalReadings: number
  }>
}

export default function IoTDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null)
  const [deviceReadings, setDeviceReadings] = useState<IoTReading[]>([])
  const [deviceAlerts, setDeviceAlerts] = useState<DeviceAlert[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showAddDevice, setShowAddDevice] = useState(false)
  const [newDevice, setNewDevice] = useState({
    deviceId: '',
    deviceType: 'smart_meter' as 'smart_meter' | 'solar_panel' | 'battery' | 'ev_charger',
    location: {
      latitude: '',
      longitude: '',
      address: ''
    },
    specifications: {
      maxCapacity: '',
      accuracy: '',
      manufacturer: '',
      model: '',
      firmwareVersion: ''
    }
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/iot/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        setDashboardData(result.data)
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    }
  }

  const fetchDeviceDetails = async (deviceId: string) => {
    try {
      const token = localStorage.getItem('auth_token')
      
      // Fetch readings
      const readingsResponse = await fetch(`/api/iot/readings/${deviceId}?limit=50`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      // Fetch alerts
      const alertsResponse = await fetch(`/api/iot/alerts/${deviceId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (readingsResponse.ok) {
        const readingsResult = await readingsResponse.json()
        setDeviceReadings(readingsResult.data.readings)
      }

      if (alertsResponse.ok) {
        const alertsResult = await alertsResponse.json()
        setDeviceAlerts(alertsResult.data)
      }
    } catch (error) {
      console.error('Error fetching device details:', error)
    }
  }

  const handleRegisterDevice = async () => {
    if (!newDevice.deviceId || !newDevice.location.address) return

    setIsLoading(true)
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/iot/devices', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceId: newDevice.deviceId,
          deviceType: newDevice.deviceType,
          location: {
            latitude: parseFloat(newDevice.location.latitude) || 0,
            longitude: parseFloat(newDevice.location.longitude) || 0,
            address: newDevice.location.address
          },
          specifications: {
            maxCapacity: parseFloat(newDevice.specifications.maxCapacity) || 100,
            accuracy: parseFloat(newDevice.specifications.accuracy) || 95,
            manufacturer: newDevice.specifications.manufacturer || 'Unknown',
            model: newDevice.specifications.model || 'Unknown',
            firmwareVersion: newDevice.specifications.firmwareVersion || '1.0.0'
          }
        })
      })

      if (response.ok) {
        setShowAddDevice(false)
        setNewDevice({
          deviceId: '',
          deviceType: 'smart_meter',
          location: { latitude: '', longitude: '', address: '' },
          specifications: { maxCapacity: '', accuracy: '', manufacturer: '', model: '', firmwareVersion: '' }
        })
        fetchDashboardData()
      }
    } catch (error) {
      console.error('Error registering device:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSimulateReading = async (deviceId: string) => {
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/iot/simulate-reading', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceId,
          readingType: 'production',
          baseValue: 10,
          variance: 20
        })
      })

      if (response.ok) {
        fetchDashboardData()
        if (selectedDevice === deviceId) {
          fetchDeviceDetails(deviceId)
        }
      }
    } catch (error) {
      console.error('Error simulating reading:', error)
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'smart_meter': return <Activity className="h-5 w-5" />
      case 'solar_panel': return <Sun className="h-5 w-5" />
      case 'battery': return <Battery className="h-5 w-5" />
      case 'ev_charger': return <Car className="h-5 w-5" />
      default: return <Smartphone className="h-5 w-5" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800'
      case 'offline': return 'bg-red-100 text-red-800'
      case 'error': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-blue-100 text-blue-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'critical': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Devices</p>
                <p className="text-2xl font-bold">{dashboardData.summary.totalDevices}</p>
              </div>
              <Smartphone className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Online</p>
                <p className="text-2xl font-bold text-green-600">{dashboardData.summary.onlineDevices}</p>
              </div>
              <Wifi className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-blue-600">{dashboardData.summary.verifiedDevices}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Alerts</p>
                <p className="text-2xl font-bold text-orange-600">{dashboardData.summary.totalAlerts}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Devices List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>IoT Devices</CardTitle>
          <Dialog open={showAddDevice} onOpenChange={setShowAddDevice}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Device
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Register New Device</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="deviceId">Device ID</Label>
                  <Input
                    id="deviceId"
                    value={newDevice.deviceId}
                    onChange={(e) => setNewDevice({...newDevice, deviceId: e.target.value})}
                    placeholder="Enter unique device ID"
                  />
                </div>

                <div>
                  <Label htmlFor="deviceType">Device Type</Label>
                  <Select value={newDevice.deviceType} onValueChange={(value) => setNewDevice({...newDevice, deviceType: value as any})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="smart_meter">Smart Meter</SelectItem>
                      <SelectItem value="solar_panel">Solar Panel</SelectItem>
                      <SelectItem value="battery">Battery</SelectItem>
                      <SelectItem value="ev_charger">EV Charger</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={newDevice.location.address}
                    onChange={(e) => setNewDevice({...newDevice, location: {...newDevice.location, address: e.target.value}})}
                    placeholder="Device installation address"
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor="latitude">Latitude</Label>
                    <Input
                      id="latitude"
                      type="number"
                      value={newDevice.location.latitude}
                      onChange={(e) => setNewDevice({...newDevice, location: {...newDevice.location, latitude: e.target.value}})}
                      placeholder="0.0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="longitude">Longitude</Label>
                    <Input
                      id="longitude"
                      type="number"
                      value={newDevice.location.longitude}
                      onChange={(e) => setNewDevice({...newDevice, location: {...newDevice.location, longitude: e.target.value}})}
                      placeholder="0.0"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="maxCapacity">Max Capacity (kW)</Label>
                  <Input
                    id="maxCapacity"
                    type="number"
                    value={newDevice.specifications.maxCapacity}
                    onChange={(e) => setNewDevice({...newDevice, specifications: {...newDevice.specifications, maxCapacity: e.target.value}})}
                    placeholder="100"
                  />
                </div>

                <Button onClick={handleRegisterDevice} disabled={isLoading} className="w-full">
                  {isLoading ? 'Registering...' : 'Register Device'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dashboardData.devices.map((deviceData) => (
              <div key={deviceData.device.deviceId} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {getDeviceIcon(deviceData.device.deviceType)}
                    <div>
                      <h3 className="font-medium">{deviceData.device.deviceId}</h3>
                      <p className="text-sm text-gray-500 capitalize">{deviceData.device.deviceType.replace('_', ' ')}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(deviceData.device.connectionStatus)}>
                      {deviceData.device.connectionStatus === 'online' ? <Wifi className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
                      {deviceData.device.connectionStatus}
                    </Badge>
                    {deviceData.device.isVerified && (
                      <Badge className="bg-blue-100 text-blue-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Total Readings</p>
                    <p className="font-medium">{deviceData.totalReadings}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Recent Readings</p>
                    <p className="font-medium">{deviceData.recentReadings.length}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Alerts</p>
                    <p className="font-medium text-orange-600">{deviceData.unacknowledgedAlerts}</p>
                  </div>
                </div>

                <div className="flex gap-2 mt-3">
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      setSelectedDevice(deviceData.device.deviceId)
                      fetchDeviceDetails(deviceData.device.deviceId)
                    }}
                  >
                    View Details
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleSimulateReading(deviceData.device.deviceId)}
                  >
                    <Zap className="h-3 w-3 mr-1" />
                    Simulate Reading
                  </Button>
                </div>

                {selectedDevice === deviceData.device.deviceId && (
                  <div className="mt-4 border-t pt-4">
                    <Tabs defaultValue="readings">
                      <TabsList>
                        <TabsTrigger value="readings">Recent Readings</TabsTrigger>
                        <TabsTrigger value="alerts">Alerts</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="readings" className="space-y-2">
                        {deviceReadings.slice(0, 5).map((reading) => (
                          <div key={reading.id} className="flex justify-between items-center text-sm border-b pb-2">
                            <div>
                              <span className="capitalize">{reading.readingType.replace('_', ' ')}</span>
                              <span className="text-gray-500 ml-2">
                                {new Date(reading.timestamp).toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{reading.value} {reading.unit}</span>
                              <Badge className={reading.verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                                {reading.verified ? 'Verified' : 'Pending'}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </TabsContent>
                      
                      <TabsContent value="alerts" className="space-y-2">
                        {deviceAlerts.slice(0, 5).map((alert) => (
                          <div key={alert.id} className="border-b pb-2">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm font-medium">{alert.message}</p>
                                <p className="text-xs text-gray-500">
                                  {new Date(alert.timestamp).toLocaleString()}
                                </p>
                              </div>
                              <Badge className={getSeverityColor(alert.severity)}>
                                {alert.severity}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </TabsContent>
                    </Tabs>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
