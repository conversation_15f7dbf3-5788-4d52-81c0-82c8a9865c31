'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  PieChart, 
  Leaf, 
  Zap,
  DollarSign,
  Target,
  Award,
  AlertCircle
} from 'lucide-react'

interface CarbonFootprint {
  userId: string
  period: string
  totalEnergyProduced: number
  totalEnergyConsumed: number
  renewablePercentage: number
  carbonSaved: number
  carbonEmitted: number
  netCarbonImpact: number
  equivalentTrees: number
  equivalentCarMiles: number
  timestamp: Date
}

interface PerformanceAnalytics {
  userId: string
  period: string
  energyEfficiency: number
  tradingEfficiency: number
  profitability: number
  marketShare: number
  reliability: number
  recommendations: string[]
  benchmarkComparison: {
    userValue: number
    averageValue: number
    topPercentile: number
    ranking: number
  }
  timestamp: Date
}

interface MarketInsight {
  id: string
  insightType: 'price_trend' | 'volume_spike' | 'supply_demand' | 'efficiency_alert' | 'carbon_milestone'
  title: string
  description: string
  severity: 'info' | 'warning' | 'critical'
  data: any
  timestamp: Date
}

interface EnergyForecast {
  forecastType: 'production' | 'consumption' | 'price' | 'demand'
  period: string
  predictions: Array<{
    timestamp: Date
    value: number
    confidence: number
    upperBound: number
    lowerBound: number
  }>
  accuracy: number
  generatedAt: Date
}

interface UserInsights {
  insights: MarketInsight[]
  forecasts: EnergyForecast[]
  carbonFootprint: CarbonFootprint
  performance: PerformanceAnalytics
}

export default function AnalyticsDashboard() {
  const [userInsights, setUserInsights] = useState<UserInsights | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<'24h' | '7d' | '30d' | '1y'>('30d')
  const [selectedForecast, setSelectedForecast] = useState<'production' | 'consumption' | 'price' | 'demand'>('production')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    fetchUserInsights()
  }, [selectedPeriod])

  const fetchUserInsights = async () => {
    setIsLoading(true)
    try {
      const token = localStorage.getItem('auth_token')
      
      // Fetch comprehensive user insights
      const insightsResponse = await fetch('/api/analytics/user-insights', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (insightsResponse.ok) {
        const result = await insightsResponse.json()
        setUserInsights(result.data)
      }
    } catch (error) {
      console.error('Error fetching user insights:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchForecast = async () => {
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch(`/api/analytics/forecast?type=${selectedForecast}&period=${selectedPeriod}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        // Update forecasts in userInsights
        if (userInsights) {
          const updatedForecasts = userInsights.forecasts.filter(f => f.forecastType !== selectedForecast)
          updatedForecasts.push(result.data)
          setUserInsights({
            ...userInsights,
            forecasts: updatedForecasts
          })
        }
      }
    } catch (error) {
      console.error('Error fetching forecast:', error)
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'price_trend': return <TrendingUp className="h-5 w-5" />
      case 'volume_spike': return <BarChart3 className="h-5 w-5" />
      case 'supply_demand': return <PieChart className="h-5 w-5" />
      case 'efficiency_alert': return <Zap className="h-5 w-5" />
      case 'carbon_milestone': return <Leaf className="h-5 w-5" />
      default: return <AlertCircle className="h-5 w-5" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info': return 'bg-blue-100 text-blue-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'critical': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatNumber = (num: number, decimals: number = 2) => {
    return num.toFixed(decimals)
  }

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`
  }

  if (isLoading || !userInsights) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        <Select value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as any)}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">24 Hours</SelectItem>
            <SelectItem value="7d">7 Days</SelectItem>
            <SelectItem value="30d">30 Days</SelectItem>
            <SelectItem value="1y">1 Year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="carbon">Carbon Impact</TabsTrigger>
          <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Energy Efficiency</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatPercentage(userInsights.performance.energyEfficiency)}
                    </p>
                  </div>
                  <Zap className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Trading Efficiency</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {formatPercentage(userInsights.performance.tradingEfficiency)}
                    </p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Profitability</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {formatPercentage(userInsights.performance.profitability)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Carbon Saved</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatNumber(userInsights.carbonFootprint.carbonSaved)} kg
                    </p>
                  </div>
                  <Leaf className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userInsights.insights.slice(0, 3).map((insight) => (
                  <div key={insight.id} className="flex items-start gap-3 p-3 border rounded-lg">
                    <div className="mt-1">
                      {getInsightIcon(insight.insightType)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{insight.title}</h4>
                        <Badge className={getSeverityColor(insight.severity)}>
                          {insight.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">{insight.description}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {new Date(insight.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Energy Efficiency</span>
                    <span className="text-sm font-bold">{formatPercentage(userInsights.performance.energyEfficiency)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${userInsights.performance.energyEfficiency}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Trading Efficiency</span>
                    <span className="text-sm font-bold">{formatPercentage(userInsights.performance.tradingEfficiency)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${userInsights.performance.tradingEfficiency}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Reliability</span>
                    <span className="text-sm font-bold">{formatPercentage(userInsights.performance.reliability)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{ width: `${userInsights.performance.reliability}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Benchmark Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">#{userInsights.performance.benchmarkComparison.ranking}</div>
                    <div className="text-sm text-gray-500">Your Ranking</div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Your Score</span>
                      <span className="font-medium">{formatNumber(userInsights.performance.benchmarkComparison.userValue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Network Average</span>
                      <span className="font-medium">{formatNumber(userInsights.performance.benchmarkComparison.averageValue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Top 10%</span>
                      <span className="font-medium">{formatNumber(userInsights.performance.benchmarkComparison.topPercentile)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userInsights.performance.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <Award className="h-5 w-5 text-blue-600 mt-0.5" />
                    <p className="text-sm">{recommendation}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Carbon Impact Tab */}
        <TabsContent value="carbon" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <Leaf className="h-12 w-12 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {formatNumber(userInsights.carbonFootprint.carbonSaved)} kg
                  </div>
                  <div className="text-sm text-gray-500">CO₂ Saved</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatNumber(userInsights.carbonFootprint.equivalentTrees)}
                  </div>
                  <div className="text-sm text-gray-500">Equivalent Trees</div>
                  <div className="text-xs text-gray-400 mt-1">Trees planted for a year</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatNumber(userInsights.carbonFootprint.equivalentCarMiles)}
                  </div>
                  <div className="text-sm text-gray-500">Car Miles Offset</div>
                  <div className="text-xs text-gray-400 mt-1">Miles of driving offset</div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Carbon Footprint Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Energy Produced</span>
                      <span className="text-sm">{formatNumber(userInsights.carbonFootprint.totalEnergyProduced)} kWh</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Energy Consumed</span>
                      <span className="text-sm">{formatNumber(userInsights.carbonFootprint.totalEnergyConsumed)} kWh</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Renewable %</span>
                      <span className="text-sm font-bold text-green-600">
                        {formatPercentage(userInsights.carbonFootprint.renewablePercentage)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Carbon Saved</span>
                      <span className="text-sm text-green-600">-{formatNumber(userInsights.carbonFootprint.carbonSaved)} kg</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Carbon Emitted</span>
                      <span className="text-sm text-red-600">+{formatNumber(userInsights.carbonFootprint.carbonEmitted)} kg</span>
                    </div>
                    <div className="flex justify-between mb-2 border-t pt-2">
                      <span className="text-sm font-bold">Net Impact</span>
                      <span className={`text-sm font-bold ${userInsights.carbonFootprint.netCarbonImpact < 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {userInsights.carbonFootprint.netCarbonImpact < 0 ? '-' : '+'}{formatNumber(Math.abs(userInsights.carbonFootprint.netCarbonImpact))} kg
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Forecasts Tab */}
        <TabsContent value="forecasts" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Energy Forecasts</h3>
            <div className="flex gap-2">
              <Select value={selectedForecast} onValueChange={(value) => setSelectedForecast(value as any)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="production">Production</SelectItem>
                  <SelectItem value="consumption">Consumption</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="demand">Demand</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={fetchForecast} size="sm">
                Update Forecast
              </Button>
            </div>
          </div>

          {userInsights.forecasts.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {userInsights.forecasts.map((forecast, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="capitalize">
                      {forecast.forecastType} Forecast
                    </CardTitle>
                    <div className="text-sm text-gray-500">
                      Accuracy: {formatPercentage(forecast.accuracy * 100)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {forecast.predictions.slice(0, 5).map((prediction, idx) => (
                        <div key={idx} className="flex justify-between items-center text-sm">
                          <span>{new Date(prediction.timestamp).toLocaleDateString()}</span>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{formatNumber(prediction.value)}</span>
                            <Badge className={`text-xs ${prediction.confidence > 0.8 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                              {formatPercentage(prediction.confidence * 100)}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Market Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userInsights.insights.map((insight) => (
                  <div key={insight.id} className="border rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="mt-1">
                        {getInsightIcon(insight.insightType)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{insight.title}</h4>
                          <Badge className={getSeverityColor(insight.severity)}>
                            {insight.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{insight.description}</p>
                        <div className="flex justify-between items-center text-xs text-gray-400">
                          <span className="capitalize">{insight.insightType.replace('_', ' ')}</span>
                          <span>{new Date(insight.timestamp).toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
