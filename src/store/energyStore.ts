import { create } from 'zustand'
import { useEffect } from 'react'

declare global {
  interface Window {
    __maschainMvpSim?: boolean;
  }
}

export interface EnergyReading {
  id: string
  meterId: string
  value: number
  type: 'production' | 'consumption'
  timestamp: number
  verified: boolean
}

export interface EnergyOffer {
  id: string
  seller: string
  energyAmount: number
  pricePerKwh: number
  offerType: 'immediate' | 'scheduled' | 'recurring'
  status: 'active' | 'completed' | 'cancelled' | 'expired'
  createdAt: number
  expiresAt: number
  filledAmount: number
}

export interface EnergyStats {
  totalProduction: number
  totalConsumption: number
  currentBalance: number
  creditsEarned: number
  creditsSpent: number
  carbonOffset: number
}

export interface MarketData {
  totalOffers: number
  totalVolumeTraded: number
  averagePrice: number
  activeOffers: EnergyOffer[]
  recentTrades: any[]
  priceHistory: { timestamp: number; price: number }[]
}

interface EnergyStore {
  // State
  energyData: EnergyStats
  marketData: MarketData
  userOffers: EnergyOffer[]
  recentReadings: EnergyReading[]
  isLoading: boolean
  error: string | null

  // Actions
  fetchEnergyData: () => Promise<void>
  fetchMarketData: () => Promise<void>
  fetchUserOffers: () => Promise<void>
  createOffer: (offer: Omit<EnergyOffer, 'id' | 'createdAt' | 'status' | 'filledAmount'>) => Promise<void>
  cancelOffer: (offerId: string) => Promise<void>
  executeTrade: (offerId: string, amount: number) => Promise<void>
  submitReading: (reading: Omit<EnergyReading, 'id' | 'timestamp' | 'verified'>) => Promise<void>
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

export const useEnergyStore = create<EnergyStore>((set, get) => ({
  // Initial state
  energyData: {
    totalProduction: 0,
    totalConsumption: 0,
    currentBalance: 0,
    creditsEarned: 0,
    creditsSpent: 0,
    carbonOffset: 0,
  },
  marketData: {
    totalOffers: 0,
    totalVolumeTraded: 0,
    averagePrice: 0,
    activeOffers: [],
    recentTrades: [],
    priceHistory: [],
  },
  userOffers: [],
  recentReadings: [],
  isLoading: false,
  error: null,

  // Actions
  fetchEnergyData: async () => {
    set({ isLoading: true, error: null })
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/energy/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch energy data')
      }

      const result = await response.json()
      const energyData: EnergyStats = result.data

      set({ energyData, isLoading: false })
    } catch (error) {
      console.error('Error fetching energy data:', error)
      // Fallback to mock data for demo
      const energyData: EnergyStats = {
        totalProduction: 1250.5,
        totalConsumption: 980.2,
        currentBalance: 270.3,
        creditsEarned: 1250,
        creditsSpent: 980,
        carbonOffset: 625.25,
      }
      set({ energyData, isLoading: false })
    }
  },

  fetchMarketData: async () => {
    set({ isLoading: true, error: null })
    try {
      // Fetch market offers
      const offersResponse = await fetch('/api/market/offers?limit=20')
      const statsResponse = await fetch('/api/market/stats')
      const priceHistoryResponse = await fetch('/api/market/price-history?period=24h')

      if (!offersResponse.ok || !statsResponse.ok || !priceHistoryResponse.ok) {
        throw new Error('Failed to fetch market data')
      }

      const offersResult = await offersResponse.json()
      const statsResult = await statsResponse.json()
      const priceHistoryResult = await priceHistoryResponse.json()

      const marketData: MarketData = {
        totalOffers: statsResult.data.totalOffers || 15,
        totalVolumeTraded: statsResult.data.totalVolumeTraded || 5420.8,
        averagePrice: statsResult.data.averagePrice || 0.115,
        activeOffers: offersResult.data.offers || [],
        recentTrades: [],
        priceHistory: priceHistoryResult.data || [],
      }

      set({ marketData, isLoading: false })
    } catch (error) {
      console.error('Error fetching market data:', error)
      // Fallback to mock data for demo
      const mockOffers: EnergyOffer[] = [
        {
          id: '1',
          seller: 'Solar Farm A',
          energyAmount: 100,
          pricePerKwh: 0.12,
          offerType: 'immediate',
          status: 'active',
          createdAt: Date.now() - 3600000,
          expiresAt: Date.now() + 7200000,
          filledAmount: 0,
        },
        {
          id: '2',
          seller: 'Wind Farm B',
          energyAmount: 250,
          pricePerKwh: 0.10,
          offerType: 'immediate',
          status: 'active',
          createdAt: Date.now() - 1800000,
          expiresAt: Date.now() + 5400000,
          filledAmount: 50,
        },
      ]

      const priceHistory = Array.from({ length: 24 }, (_, i) => ({
        timestamp: Date.now() - (23 - i) * 3600000,
        price: 0.08 + Math.random() * 0.08,
      }))

      const marketData: MarketData = {
        totalOffers: 15,
        totalVolumeTraded: 5420.8,
        averagePrice: 0.115,
        activeOffers: mockOffers,
        recentTrades: [],
        priceHistory,
      }

      set({ marketData, isLoading: false })
    }
  },

  fetchUserOffers: async () => {
    set({ isLoading: true, error: null })
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Mock user offers
      const userOffers: EnergyOffer[] = [
        {
          id: 'user-1',
          seller: 'You',
          energyAmount: 50,
          pricePerKwh: 0.13,
          offerType: 'immediate',
          status: 'active',
          createdAt: Date.now() - 900000,
          expiresAt: Date.now() + 6300000,
          filledAmount: 15,
        },
      ]
      
      set({ userOffers, isLoading: false })
    } catch (error) {
      set({ error: 'Failed to fetch user offers', isLoading: false })
    }
  },

  createOffer: async (offerData) => {
    set({ isLoading: true, error: null })
    try {
      const token = localStorage.getItem('auth_token')
      const response = await fetch('/api/market/offers', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          energyAmount: offerData.energyAmount,
          pricePerKwh: offerData.pricePerKwh,
          offerType: offerData.offerType,
          duration: Math.floor((offerData.expiresAt - Date.now()) / (1000 * 60 * 60)) // Convert to hours
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create offer')
      }

      const result = await response.json()
      const newOffer: EnergyOffer = result.data

      set(state => ({
        userOffers: [...state.userOffers, newOffer],
        marketData: {
          ...state.marketData,
          activeOffers: [...state.marketData.activeOffers, newOffer],
        },
        isLoading: false,
      }))
    } catch (error) {
      console.error('Error creating offer:', error)
      set({ error: 'Failed to create offer', isLoading: false })
    }
  },

  cancelOffer: async (offerId) => {
    set({ isLoading: true, error: null })
    try {
      // Simulate blockchain transaction
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      set(state => ({
        userOffers: state.userOffers.map(offer =>
          offer.id === offerId ? { ...offer, status: 'cancelled' as const } : offer
        ),
        marketData: {
          ...state.marketData,
          activeOffers: state.marketData.activeOffers.filter(offer => offer.id !== offerId),
        },
        isLoading: false,
      }))
    } catch (error) {
      set({ error: 'Failed to cancel offer', isLoading: false })
    }
  },

  executeTrade: async (offerId, amount) => {
    set({ isLoading: true, error: null })
    try {
      // Simulate blockchain transaction
      await new Promise(resolve => setTimeout(resolve, 2500))
      
      set(state => ({
        marketData: {
          ...state.marketData,
          activeOffers: state.marketData.activeOffers.map(offer =>
            offer.id === offerId
              ? { ...offer, filledAmount: offer.filledAmount + amount }
              : offer
          ),
        },
        isLoading: false,
      }))
    } catch (error) {
      set({ error: 'Failed to execute trade', isLoading: false })
    }
  },

  submitReading: async (readingData) => {
    set({ isLoading: true, error: null })
    try {
      // Simulate blockchain transaction
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newReading: EnergyReading = {
        ...readingData,
        id: `reading-${Date.now()}`,
        timestamp: Date.now(),
        verified: true,
      }
      
      set(state => ({
        recentReadings: [newReading, ...state.recentReadings.slice(0, 9)],
        isLoading: false,
      }))
    } catch (error) {
      set({ error: 'Failed to submit reading', isLoading: false })
    }
  },

  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
}))

// IoT/market simulation: periodically update stats (browser only)
if (typeof window !== 'undefined' && !window.__maschainMvpSim) {
  window.__maschainMvpSim = true;
  setInterval(() => {
    useEnergyStore.setState((state) => ({
      energyData: {
        ...state.energyData,
        totalProduction: state.energyData.totalProduction + Math.random() * 2,
        totalConsumption: state.energyData.totalConsumption + Math.random() * 1.5,
        currentBalance: Math.max(
          state.energyData.currentBalance + (Math.random() - 0.5) * 2,
          0
        ),
        creditsEarned: state.energyData.creditsEarned + Math.random() * 2,
        creditsSpent: state.energyData.creditsSpent + Math.random() * 1.5,
        carbonOffset: state.energyData.carbonOffset + Math.random() * 1.2,
      },
      marketData: {
        ...state.marketData,
        totalOffers: 10 + Math.floor(Math.random() * 10),
        totalVolumeTraded: state.marketData.totalVolumeTraded + Math.random() * 10,
        averagePrice: 0.08 + Math.random() * 0.12,
      },
    }))
  }, 3000)
}
