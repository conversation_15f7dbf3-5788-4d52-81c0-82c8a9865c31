# Environment Configuration
NODE_ENV=development

# Frontend Configuration
NEXT_PUBLIC_MASCHAIN_ENVIRONMENT=testnet
NEXT_PUBLIC_MASCHAIN_EXPLORER=https://explorer-testnet.maschain.com

# Backend Configuration
PORT=3001
FRONTEND_URL=http://localhost:3000

# MasChain L1 Blockchain Configuration
MASCHAIN_ENVIRONMENT=testnet
MASCHAIN_API_URL=https://service-testnet.maschain.com
MASCHAIN_PORTAL_URL=https://portal-testnet.maschain.com
MASCHAIN_EXPLORER_URL=https://explorer-testnet.maschain.com

# MasChain API Credentials (Get these from https://portal.maschain.com)
MASCHAIN_API_KEY=your_api_key_here
MASCHAIN_API_SECRET=your_api_secret_here
MASCHAIN_PROJECT_ID=your_project_id_here

# Energy Trading Contract IDs (Will be generated after deployment)
ENERGY_CREDIT_CONTRACT_ID=
ENERGY_MARKET_CONTRACT_ID=
ENERGY_ORACLE_CONTRACT_ID=

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/maschain_energy
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# IoT Configuration
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_USERNAME=
MQTT_PASSWORD=

# External APIs
WEATHER_API_KEY=your-weather-api-key
GRID_API_ENDPOINT=https://api.grid-provider.com
GRID_API_KEY=your-grid-api-key

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
