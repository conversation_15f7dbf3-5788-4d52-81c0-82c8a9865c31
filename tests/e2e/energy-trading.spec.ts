import { test, expect } from '@playwright/test'

test.describe('Energy Trading Platform E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000')
  })

  test.describe('Landing Page', () => {
    test('should display landing page with live stats', async ({ page }) => {
      // Check main heading
      await expect(page.locator('h1')).toContainText('Trade Energy Like Never Before')
      
      // Check live stats are displayed
      await expect(page.locator('[data-testid="live-stats"]')).toBeVisible()
      
      // Check navigation links
      await expect(page.locator('a[href="/trading"]')).toBeVisible()
      await expect(page.locator('a[href="/iot"]')).toBeVisible()
      await expect(page.locator('a[href="/analytics"]')).toBeVisible()
      
      // Check CTA buttons
      await expect(page.locator('a[href="/dashboard"]')).toBeVisible()
    })

    test('should navigate to dashboard when CTA is clicked', async ({ page }) => {
      await page.click('a[href="/dashboard"]')
      await expect(page).toHaveURL(/.*dashboard/)
    })

    test('should display live updating stats', async ({ page }) => {
      // Get initial active trades value
      const initialTrades = await page.locator('[data-testid="active-trades"]').textContent()
      
      // Wait for stats to update (they update every 3 seconds)
      await page.waitForTimeout(4000)
      
      // Check if value has changed (indicating live updates)
      const updatedTrades = await page.locator('[data-testid="active-trades"]').textContent()
      
      // Values should be different due to live updates
      expect(initialTrades).not.toBe(updatedTrades)
    })
  })

  test.describe('Trading Dashboard', () => {
    test.beforeEach(async ({ page }) => {
      // Navigate to trading dashboard
      await page.goto('http://localhost:3000/trading')
    })

    test('should display trading interface', async ({ page }) => {
      // Check main components are visible
      await expect(page.locator('h1')).toContainText('Energy Trading')
      await expect(page.locator('[data-testid="market-stats"]')).toBeVisible()
      await expect(page.locator('[data-testid="order-book"]')).toBeVisible()
      await expect(page.locator('[data-testid="trading-form"]')).toBeVisible()
    })

    test('should create a new energy offer', async ({ page }) => {
      // Click create offer button
      await page.click('[data-testid="create-offer-btn"]')
      
      // Fill out the form
      await page.fill('[data-testid="energy-amount-input"]', '100')
      await page.fill('[data-testid="price-input"]', '0.15')
      await page.selectOption('[data-testid="offer-type-select"]', 'immediate')
      await page.fill('[data-testid="duration-input"]', '24')
      
      // Submit the form
      await page.click('[data-testid="submit-offer-btn"]')
      
      // Check for success message
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
      
      // Verify offer appears in the list
      await expect(page.locator('[data-testid="user-offers"]')).toContainText('100 kWh')
      await expect(page.locator('[data-testid="user-offers"]')).toContainText('$0.15')
    })

    test('should validate form inputs', async ({ page }) => {
      // Click create offer button
      await page.click('[data-testid="create-offer-btn"]')
      
      // Try to submit without filling required fields
      await page.click('[data-testid="submit-offer-btn"]')
      
      // Check for validation errors
      await expect(page.locator('[data-testid="energy-amount-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="price-error"]')).toBeVisible()
    })

    test('should execute a trade', async ({ page }) => {
      // Assume there's an existing offer in the market
      await expect(page.locator('[data-testid="market-offers"]')).toBeVisible()
      
      // Click on an offer to trade
      await page.click('[data-testid="market-offer-item"]:first-child [data-testid="trade-btn"]')
      
      // Fill trade amount
      await page.fill('[data-testid="trade-amount-input"]', '50')
      
      // Confirm trade
      await page.click('[data-testid="confirm-trade-btn"]')
      
      // Check for success message
      await expect(page.locator('[data-testid="trade-success"]')).toBeVisible()
    })

    test('should cancel an offer', async ({ page }) => {
      // Assume user has an active offer
      await expect(page.locator('[data-testid="user-offers"]')).toBeVisible()
      
      // Click cancel button on first offer
      await page.click('[data-testid="user-offer-item"]:first-child [data-testid="cancel-btn"]')
      
      // Confirm cancellation
      await page.click('[data-testid="confirm-cancel-btn"]')
      
      // Check offer status changed to cancelled
      await expect(page.locator('[data-testid="user-offer-item"]:first-child')).toContainText('Cancelled')
    })
  })

  test.describe('IoT Dashboard', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('http://localhost:3000/iot')
    })

    test('should display IoT device management', async ({ page }) => {
      await expect(page.locator('h1')).toContainText('IoT Device Management')
      await expect(page.locator('[data-testid="device-list"]')).toBeVisible()
      await expect(page.locator('[data-testid="add-device-btn"]')).toBeVisible()
    })

    test('should register a new IoT device', async ({ page }) => {
      // Click add device button
      await page.click('[data-testid="add-device-btn"]')
      
      // Fill device registration form
      await page.fill('[data-testid="device-id-input"]', 'SOLAR_001')
      await page.fill('[data-testid="device-name-input"]', 'Solar Panel Array 1')
      await page.selectOption('[data-testid="device-type-select"]', 'solar')
      await page.fill('[data-testid="device-location-input"]', 'Rooftop North')
      
      // Submit registration
      await page.click('[data-testid="register-device-btn"]')
      
      // Check for success message
      await expect(page.locator('[data-testid="registration-success"]')).toBeVisible()
      
      // Verify device appears in list
      await expect(page.locator('[data-testid="device-list"]')).toContainText('SOLAR_001')
    })

    test('should display real-time energy readings', async ({ page }) => {
      // Check that energy readings are displayed
      await expect(page.locator('[data-testid="energy-readings"]')).toBeVisible()
      
      // Check for production and consumption data
      await expect(page.locator('[data-testid="production-reading"]')).toBeVisible()
      await expect(page.locator('[data-testid="consumption-reading"]')).toBeVisible()
      
      // Verify readings update over time
      const initialProduction = await page.locator('[data-testid="production-value"]').textContent()
      await page.waitForTimeout(5000) // Wait for reading update
      const updatedProduction = await page.locator('[data-testid="production-value"]').textContent()
      
      // Values should potentially be different (depending on simulation)
      expect(initialProduction).toBeDefined()
      expect(updatedProduction).toBeDefined()
    })
  })

  test.describe('Analytics Dashboard', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('http://localhost:3000/analytics')
    })

    test('should display analytics overview', async ({ page }) => {
      await expect(page.locator('h1')).toContainText('Energy Analytics')
      await expect(page.locator('[data-testid="analytics-charts"]')).toBeVisible()
      await expect(page.locator('[data-testid="performance-metrics"]')).toBeVisible()
    })

    test('should switch between different time periods', async ({ page }) => {
      // Check default period is selected
      await expect(page.locator('[data-testid="period-selector"]')).toBeVisible()
      
      // Switch to different period
      await page.selectOption('[data-testid="period-selector"]', '7d')
      
      // Verify charts update
      await expect(page.locator('[data-testid="analytics-charts"]')).toBeVisible()
      
      // Check that data reflects the new period
      await expect(page.locator('[data-testid="period-label"]')).toContainText('7 days')
    })

    test('should display energy forecasts', async ({ page }) => {
      // Navigate to forecasts tab
      await page.click('[data-testid="forecasts-tab"]')
      
      // Check forecast charts are displayed
      await expect(page.locator('[data-testid="forecast-charts"]')).toBeVisible()
      
      // Test forecast type selection
      await page.selectOption('[data-testid="forecast-type-select"]', 'production')
      await expect(page.locator('[data-testid="production-forecast"]')).toBeVisible()
    })
  })

  test.describe('Transaction History', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('http://localhost:3000/history')
    })

    test('should display transaction history', async ({ page }) => {
      await expect(page.locator('h1')).toContainText('Transaction History')
      await expect(page.locator('[data-testid="transaction-summary"]')).toBeVisible()
      await expect(page.locator('[data-testid="transaction-list"]')).toBeVisible()
    })

    test('should filter transactions by type', async ({ page }) => {
      // Select trade filter
      await page.selectOption('[data-testid="type-filter"]', 'trade')
      
      // Verify only trade transactions are shown
      const transactions = page.locator('[data-testid="transaction-item"]')
      const count = await transactions.count()
      
      for (let i = 0; i < count; i++) {
        await expect(transactions.nth(i)).toContainText('Trade')
      }
    })

    test('should export transaction data', async ({ page }) => {
      // Start download
      const downloadPromise = page.waitForEvent('download')
      await page.click('[data-testid="export-btn"]')
      const download = await downloadPromise
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/energy-trading-history-.*\.csv/)
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      
      await page.goto('http://localhost:3000')
      
      // Check mobile navigation
      await expect(page.locator('[data-testid="mobile-menu-btn"]')).toBeVisible()
      
      // Open mobile menu
      await page.click('[data-testid="mobile-menu-btn"]')
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      
      // Check navigation links work
      await page.click('[data-testid="mobile-menu"] a[href="/trading"]')
      await expect(page).toHaveURL(/.*trading/)
    })

    test('should work on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 })
      
      await page.goto('http://localhost:3000/trading')
      
      // Check that layout adapts to tablet size
      await expect(page.locator('[data-testid="trading-grid"]')).toBeVisible()
      await expect(page.locator('[data-testid="order-book"]')).toBeVisible()
    })
  })

  test.describe('Performance', () => {
    test('should load pages quickly', async ({ page }) => {
      const startTime = Date.now()
      await page.goto('http://localhost:3000')
      const loadTime = Date.now() - startTime
      
      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)
    })

    test('should handle real-time updates efficiently', async ({ page }) => {
      await page.goto('http://localhost:3000/trading')
      
      // Monitor network activity
      const responses = []
      page.on('response', response => {
        if (response.url().includes('/api/')) {
          responses.push(response)
        }
      })
      
      // Wait for some real-time updates
      await page.waitForTimeout(10000)
      
      // Should not have excessive API calls
      expect(responses.length).toBeLessThan(20)
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort())
      
      await page.goto('http://localhost:3000/trading')
      
      // Should show error message instead of crashing
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
      await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible()
    })

    test('should handle invalid form submissions', async ({ page }) => {
      await page.goto('http://localhost:3000/trading')
      
      // Try to create offer with invalid data
      await page.click('[data-testid="create-offer-btn"]')
      await page.fill('[data-testid="energy-amount-input"]', '-100')
      await page.fill('[data-testid="price-input"]', '-0.15')
      await page.click('[data-testid="submit-offer-btn"]')
      
      // Should show validation errors
      await expect(page.locator('[data-testid="validation-errors"]')).toBeVisible()
    })
  })
})
