# MasChain Energy Trading Platform - Production Environment Configuration
# Copy this file to .env.production and fill in your actual values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-domain.com

# =============================================================================
# MASCHAIN L1 BLOCKCHAIN CONFIGURATION
# =============================================================================

# MasChain Environment (mainnet for production)
MASCHAIN_ENVIRONMENT=mainnet

# MasChain API Credentials (obtain from https://portal.maschain.com)
MASCHAIN_API_KEY=your_production_api_key_here
MASCHAIN_API_SECRET=your_production_api_secret_here
MASCHAIN_PROJECT_ID=your_project_id_here

# MasChain Service URLs
MASCHAIN_API_URL=https://service.maschain.com
MASCHAIN_EXPLORER_URL=https://explorer.maschain.com

# Smart Contract Addresses (will be populated after deployment)
MASCHAIN_ENERGY_TOKEN_ADDRESS=
MASCHAIN_MARKETPLACE_ADDRESS=
MASCHAIN_ORACLE_ADDRESS=
MASCHAIN_DEPLOYMENT_BLOCK=

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DATABASE_URL=****************************************/maschain_energy_prod
DB_HOST=your-db-host.com
DB_PORT=5432
DB_NAME=maschain_energy_prod
DB_USER=your_db_user
DB_PASSWORD=your_secure_db_password

# Database Pool Configuration
DB_POOL_MIN=5
DB_POOL_MAX=50
DB_CONNECTION_TIMEOUT=10000
DB_IDLE_TIMEOUT=30000
DB_QUERY_TIMEOUT=60000
DB_STATEMENT_TIMEOUT=60000

# Database SSL (required for production)
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=false

# =============================================================================
# REDIS CACHE CONFIGURATION
# =============================================================================

# Redis Configuration
REDIS_URL=redis://username:password@host:6379
REDIS_HOST=your-redis-host.com
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Redis Connection Settings
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_DELAY_ON_FAILOVER=100
REDIS_MAX_RETRIES_PER_REQUEST=3

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Secret (generate a strong random string)
JWT_SECRET=your_very_long_and_secure_jwt_secret_key_here_minimum_32_characters

# JWT Expiration
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=https://your-domain.com
CORS_CREDENTIALS=true

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL=info

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_METRICS_RETENTION_HOURS=168

# Error Tracking (optional - integrate with Sentry, etc.)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=production

# =============================================================================
# IOT AND ORACLE CONFIGURATION
# =============================================================================

# MQTT Broker for IoT devices
MQTT_BROKER_URL=mqtt://your-mqtt-broker.com:1883
MQTT_USERNAME=your_mqtt_username
MQTT_PASSWORD=your_mqtt_password
MQTT_CLIENT_ID=maschain-energy-prod

# Oracle Configuration
ORACLE_VALIDATOR_THRESHOLD=3
ORACLE_DATA_RETENTION_DAYS=90
ORACLE_MAX_DATA_AGE_SECONDS=300

# Energy Meter Validation
METER_SIGNATURE_REQUIRED=true
METER_DATA_ENCRYPTION=true

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Email Service (for notifications)
SMTP_HOST=your-smtp-host.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
FROM_EMAIL=<EMAIL>

# SMS Service (optional)
SMS_PROVIDER=twilio
SMS_ACCOUNT_SID=your_twilio_account_sid
SMS_AUTH_TOKEN=your_twilio_auth_token
SMS_FROM_NUMBER=+**********

# Push Notifications (optional)
PUSH_NOTIFICATION_KEY=your_push_notification_key

# =============================================================================
# BACKUP AND DISASTER RECOVERY
# =============================================================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key

# =============================================================================
# SCALING AND LOAD BALANCING
# =============================================================================

# Cluster Configuration
CLUSTER_ENABLED=true
CLUSTER_WORKERS=auto

# Load Balancer Health Check
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_INTERVAL=30

# Session Store (for multi-instance deployments)
SESSION_STORE=redis
SESSION_SECRET=your_session_secret_key

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Trading Features
ENABLE_SPOT_TRADING=true
ENABLE_LIMIT_ORDERS=true
ENABLE_RECURRING_ORDERS=true
ENABLE_BULK_TRADING=true

# Analytics Features
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_FORECASTING=true
ENABLE_MARKET_INSIGHTS=true

# IoT Features
ENABLE_REAL_TIME_MONITORING=true
ENABLE_DEVICE_MANAGEMENT=true
ENABLE_AUTOMATED_TRADING=true

# =============================================================================
# COMPLIANCE AND REGULATORY
# =============================================================================

# KYC/AML Configuration
KYC_REQUIRED=true
KYC_PROVIDER=your_kyc_provider
KYC_API_KEY=your_kyc_api_key

# Regulatory Reporting
REGULATORY_REPORTING_ENABLED=true
REGULATORY_REPORT_SCHEDULE=0 0 1 * *

# Data Privacy
GDPR_COMPLIANCE=true
DATA_RETENTION_POLICY_DAYS=2555

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment Environment
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_TIMESTAMP=

# Container Configuration (if using Docker)
CONTAINER_MEMORY_LIMIT=2048m
CONTAINER_CPU_LIMIT=1000m

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30000
HEALTH_CHECK_RETRIES=3

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Weather API (for solar forecasting)
WEATHER_API_KEY=your_weather_api_key
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# Grid Operator API (if available)
GRID_OPERATOR_API_URL=https://api.grid-operator.com
GRID_OPERATOR_API_KEY=your_grid_api_key

# Energy Price Feed
ENERGY_PRICE_FEED_URL=https://api.energy-prices.com
ENERGY_PRICE_FEED_KEY=your_price_feed_key

# =============================================================================
# DEVELOPMENT AND TESTING OVERRIDES
# =============================================================================

# Set to true only for staging/testing environments
ENABLE_DEBUG_ENDPOINTS=false
ENABLE_TEST_DATA=false
SKIP_BLOCKCHAIN_VERIFICATION=false

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all placeholder values with your actual production values
# 2. Keep this file secure and never commit it to version control
# 3. Use environment-specific values for different deployment stages
# 4. Regularly rotate secrets and API keys
# 5. Monitor all external service dependencies
# 6. Test all configurations in staging before production deployment
