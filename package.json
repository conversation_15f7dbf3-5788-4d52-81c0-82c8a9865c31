{"name": "maschain-energy-trading", "version": "0.1.0", "description": "Blockchain-based energy trading platform using Maaschain", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "next build && cp -r .next/static out/_next/static && cp -r .next/standalone out/", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit", "smart-contracts:build": "cd smart-contracts && cargo build-bpf", "smart-contracts:test": "cd smart-contracts && cargo test", "iot:start": "cd iot-integration && python meter-client/meter_client.py", "backend:dev": "cd backend && npm run dev", "maschain:deploy": "ts-node scripts/deploy-maschain-contracts.ts", "maschain:deploy:testnet": "MASCHAIN_ENVIRONMENT=testnet ts-node scripts/deploy-maschain-contracts.ts", "maschain:deploy:mainnet": "MASCHAIN_ENVIRONMENT=mainnet ts-node scripts/deploy-maschain-contracts.ts", "maschain:init": "ts-node scripts/init-maschain.ts"}, "dependencies": {"@heroicons/react": "^2.0.18", "axios": "^1.6.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.525.0", "next": "^14.2.29", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "tailwind-merge": "^3.3.1", "zustand": "^4.4.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@types/node": "20.8.0", "@types/react": "18.2.25", "@types/react-dom": "18.2.11", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.16", "eslint": "8.51.0", "eslint-config-next": "14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}