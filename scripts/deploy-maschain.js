#!/usr/bin/env node

/**
 * MasChain L1 Deployment Script
 * Deploys smart contracts to MasChain mainnet/testnet
 */

const fs = require('fs')
const path = require('path')
const axios = require('axios')
require('dotenv').config()

class MasChainDeployer {
  constructor() {
    this.environment = process.env.MASCHAIN_ENVIRONMENT || 'testnet'
    this.apiKey = process.env.MASCHAIN_API_KEY
    this.apiSecret = process.env.MASCHAIN_API_SECRET
    this.projectId = process.env.MASCHAIN_PROJECT_ID
    
    this.baseURL = this.environment === 'mainnet' 
      ? 'https://service.maschain.com'
      : 'https://service-testnet.maschain.com'
    
    this.deploymentRecord = {
      timestamp: new Date().toISOString(),
      environment: this.environment,
      contracts: {},
      transactions: []
    }
  }

  /**
   * Validate deployment prerequisites
   */
  validatePrerequisites() {
    console.log('🔍 Validating deployment prerequisites...')
    
    const required = ['MASCHAIN_API_KEY', 'MASCHAIN_API_SECRET', 'MASCHAIN_PROJECT_ID']
    const missing = required.filter(key => !process.env[key])
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
    }
    
    console.log('✅ Prerequisites validated')
  }

  /**
   * Create API client with authentication
   */
  createApiClient() {
    return axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-API-Secret': this.apiSecret,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    })
  }

  /**
   * Deploy Energy Credit Token (EC)
   */
  async deployEnergyToken() {
    console.log('🪙 Deploying Energy Credit Token...')
    
    const client = this.createApiClient()
    
    try {
      const tokenData = {
        name: 'Energy Credit',
        symbol: 'EC',
        decimals: 3, // 0.001 kWh precision
        totalSupply: 1000000000, // 1 billion tokens
        description: 'Energy Credit tokens representing 1 kWh of energy each',
        metadata: {
          type: 'energy_token',
          unit: 'kWh',
          precision: 3,
          project: 'maschain-energy-trading'
        }
      }
      
      const response = await client.post('/api/v1/token/create', tokenData)
      
      if (response.data.success) {
        const contractAddress = response.data.data.contractAddress
        const txHash = response.data.data.transactionHash
        
        this.deploymentRecord.contracts.energyToken = {
          address: contractAddress,
          transactionHash: txHash,
          deployedAt: new Date().toISOString()
        }
        
        console.log(`✅ Energy Token deployed at: ${contractAddress}`)
        console.log(`📝 Transaction: ${txHash}`)
        
        return { contractAddress, txHash }
      } else {
        throw new Error(`Token deployment failed: ${response.data.message}`)
      }
    } catch (error) {
      console.error('❌ Energy Token deployment failed:', error.message)
      throw error
    }
  }

  /**
   * Deploy Energy Marketplace Contract
   */
  async deployMarketplace() {
    console.log('🏪 Deploying Energy Marketplace...')
    
    const client = this.createApiClient()
    
    try {
      const marketplaceData = {
        template: 'MARKETPLACE',
        name: 'Energy Trading Marketplace',
        description: 'Decentralized marketplace for peer-to-peer energy trading',
        parameters: {
          feePercentage: 250, // 2.5% trading fee
          minTradeAmount: 100, // 0.1 kWh minimum
          maxTradeAmount: 1000000, // 1000 kWh maximum
          tradingHours: {
            start: 0, // 24/7 trading
            end: 24
          }
        },
        metadata: {
          type: 'energy_marketplace',
          version: '1.0.0',
          features: ['spot_trading', 'limit_orders', 'partial_fills']
        }
      }
      
      const response = await client.post('/api/v1/contract/create', marketplaceData)
      
      if (response.data.success) {
        const contractAddress = response.data.data.contractAddress
        const txHash = response.data.data.transactionHash
        
        this.deploymentRecord.contracts.marketplace = {
          address: contractAddress,
          transactionHash: txHash,
          deployedAt: new Date().toISOString()
        }
        
        console.log(`✅ Marketplace deployed at: ${contractAddress}`)
        console.log(`📝 Transaction: ${txHash}`)
        
        return { contractAddress, txHash }
      } else {
        throw new Error(`Marketplace deployment failed: ${response.data.message}`)
      }
    } catch (error) {
      console.error('❌ Marketplace deployment failed:', error.message)
      throw error
    }
  }

  /**
   * Deploy Energy Oracle Contract
   */
  async deployOracle() {
    console.log('🔮 Deploying Energy Oracle...')
    
    const client = this.createApiClient()
    
    try {
      const oracleData = {
        template: 'ORACLE',
        name: 'Energy Data Oracle',
        description: 'Oracle for validating IoT energy meter data',
        parameters: {
          validatorThreshold: 3, // Require 3 validator confirmations
          dataRetentionDays: 90,
          maxDataAge: 300, // 5 minutes max age for readings
          requiredSignatures: 2
        },
        metadata: {
          type: 'energy_oracle',
          version: '1.0.0',
          dataTypes: ['production', 'consumption', 'grid_export', 'grid_import']
        }
      }
      
      const response = await client.post('/api/v1/contract/create', oracleData)
      
      if (response.data.success) {
        const contractAddress = response.data.data.contractAddress
        const txHash = response.data.data.transactionHash
        
        this.deploymentRecord.contracts.oracle = {
          address: contractAddress,
          transactionHash: txHash,
          deployedAt: new Date().toISOString()
        }
        
        console.log(`✅ Oracle deployed at: ${contractAddress}`)
        console.log(`📝 Transaction: ${txHash}`)
        
        return { contractAddress, txHash }
      } else {
        throw new Error(`Oracle deployment failed: ${response.data.message}`)
      }
    } catch (error) {
      console.error('❌ Oracle deployment failed:', error.message)
      throw error
    }
  }

  /**
   * Configure contract interactions
   */
  async configureContracts() {
    console.log('⚙️ Configuring contract interactions...')
    
    const client = this.createApiClient()
    
    try {
      // Set marketplace token
      if (this.deploymentRecord.contracts.energyToken && this.deploymentRecord.contracts.marketplace) {
        const configData = {
          contractAddress: this.deploymentRecord.contracts.marketplace.address,
          method: 'setEnergyToken',
          parameters: [this.deploymentRecord.contracts.energyToken.address]
        }
        
        const response = await client.post('/api/v1/contract/call', configData)
        
        if (response.data.success) {
          console.log('✅ Marketplace configured with Energy Token')
          this.deploymentRecord.transactions.push({
            type: 'configuration',
            description: 'Set energy token in marketplace',
            transactionHash: response.data.data.transactionHash,
            timestamp: new Date().toISOString()
          })
        }
      }
      
      // Set oracle in marketplace
      if (this.deploymentRecord.contracts.oracle && this.deploymentRecord.contracts.marketplace) {
        const configData = {
          contractAddress: this.deploymentRecord.contracts.marketplace.address,
          method: 'setOracle',
          parameters: [this.deploymentRecord.contracts.oracle.address]
        }
        
        const response = await client.post('/api/v1/contract/call', configData)
        
        if (response.data.success) {
          console.log('✅ Marketplace configured with Oracle')
          this.deploymentRecord.transactions.push({
            type: 'configuration',
            description: 'Set oracle in marketplace',
            transactionHash: response.data.data.transactionHash,
            timestamp: new Date().toISOString()
          })
        }
      }
      
    } catch (error) {
      console.error('❌ Contract configuration failed:', error.message)
      throw error
    }
  }

  /**
   * Verify deployments
   */
  async verifyDeployments() {
    console.log('🔍 Verifying deployments...')
    
    const client = this.createApiClient()
    
    for (const [contractName, contractInfo] of Object.entries(this.deploymentRecord.contracts)) {
      try {
        const response = await client.get(`/api/v1/contract/${contractInfo.address}`)
        
        if (response.data.success) {
          console.log(`✅ ${contractName} verified at ${contractInfo.address}`)
        } else {
          console.warn(`⚠️ ${contractName} verification failed`)
        }
      } catch (error) {
        console.error(`❌ ${contractName} verification error:`, error.message)
      }
    }
  }

  /**
   * Save deployment record
   */
  saveDeploymentRecord() {
    const deploymentDir = path.join(__dirname, '..', 'deployments')
    if (!fs.existsSync(deploymentDir)) {
      fs.mkdirSync(deploymentDir, { recursive: true })
    }
    
    const filename = `deployment-${this.environment}-${Date.now()}.json`
    const filepath = path.join(deploymentDir, filename)
    
    fs.writeFileSync(filepath, JSON.stringify(this.deploymentRecord, null, 2))
    
    console.log(`📄 Deployment record saved: ${filepath}`)
    
    // Also save as latest
    const latestPath = path.join(deploymentDir, `latest-${this.environment}.json`)
    fs.writeFileSync(latestPath, JSON.stringify(this.deploymentRecord, null, 2))
  }

  /**
   * Update environment configuration
   */
  updateEnvironmentConfig() {
    console.log('📝 Updating environment configuration...')
    
    const envUpdates = [
      `# MasChain ${this.environment.toUpperCase()} Deployment - ${new Date().toISOString()}`,
      `MASCHAIN_ENERGY_TOKEN_ADDRESS=${this.deploymentRecord.contracts.energyToken?.address || ''}`,
      `MASCHAIN_MARKETPLACE_ADDRESS=${this.deploymentRecord.contracts.marketplace?.address || ''}`,
      `MASCHAIN_ORACLE_ADDRESS=${this.deploymentRecord.contracts.oracle?.address || ''}`,
      `MASCHAIN_DEPLOYMENT_BLOCK=${Date.now()}`,
      ''
    ]
    
    const envFile = path.join(__dirname, '..', `.env.${this.environment}`)
    fs.appendFileSync(envFile, envUpdates.join('\n'))
    
    console.log(`✅ Environment config updated: ${envFile}`)
  }

  /**
   * Main deployment process
   */
  async deploy() {
    try {
      console.log(`🚀 Starting MasChain ${this.environment} deployment...`)
      
      this.validatePrerequisites()
      
      // Deploy contracts
      await this.deployEnergyToken()
      await this.deployMarketplace()
      await this.deployOracle()
      
      // Configure interactions
      await this.configureContracts()
      
      // Verify deployments
      await this.verifyDeployments()
      
      // Save records
      this.saveDeploymentRecord()
      this.updateEnvironmentConfig()
      
      console.log('🎉 Deployment completed successfully!')
      console.log('\n📋 Deployment Summary:')
      console.log(`Environment: ${this.environment}`)
      console.log(`Energy Token: ${this.deploymentRecord.contracts.energyToken?.address}`)
      console.log(`Marketplace: ${this.deploymentRecord.contracts.marketplace?.address}`)
      console.log(`Oracle: ${this.deploymentRecord.contracts.oracle?.address}`)
      
    } catch (error) {
      console.error('💥 Deployment failed:', error.message)
      
      // Save partial deployment record for debugging
      this.deploymentRecord.error = error.message
      this.saveDeploymentRecord()
      
      process.exit(1)
    }
  }
}

// Run deployment if called directly
if (require.main === module) {
  const deployer = new MasChainDeployer()
  deployer.deploy()
}

module.exports = MasChainDeployer
