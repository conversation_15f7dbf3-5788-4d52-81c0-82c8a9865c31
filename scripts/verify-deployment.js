#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> Deployment Verification Script
 * Verifies that all contracts are deployed and configured correctly
 */

const fs = require('fs')
const path = require('path')
const axios = require('axios')
require('dotenv').config()

class DeploymentVerifier {
  constructor() {
    this.environment = process.env.MASCHAIN_ENVIRONMENT || 'testnet'
    this.apiKey = process.env.MASCHAIN_API_KEY
    this.apiSecret = process.env.MASCHAIN_API_SECRET
    
    this.baseURL = this.environment === 'mainnet' 
      ? 'https://service.maschain.com'
      : 'https://service-testnet.maschain.com'
    
    this.verificationResults = {
      timestamp: new Date().toISOString(),
      environment: this.environment,
      overall: 'pending',
      contracts: {},
      integrations: {},
      performance: {}
    }
  }

  /**
   * Create API client
   */
  createApiClient() {
    return axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'X-API-Secret': this.apiSecret,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    })
  }

  /**
   * Load deployment record
   */
  loadDeploymentRecord() {
    const deploymentPath = path.join(__dirname, '..', 'deployments', `latest-${this.environment}.json`)
    
    if (!fs.existsSync(deploymentPath)) {
      throw new Error(`No deployment record found for ${this.environment}`)
    }
    
    return JSON.parse(fs.readFileSync(deploymentPath, 'utf8'))
  }

  /**
   * Verify contract deployment
   */
  async verifyContract(contractName, contractAddress) {
    console.log(`🔍 Verifying ${contractName} at ${contractAddress}...`)
    
    const client = this.createApiClient()
    
    try {
      const response = await client.get(`/api/v1/contract/${contractAddress}`)
      
      if (response.data.success) {
        const contractInfo = response.data.data
        
        this.verificationResults.contracts[contractName] = {
          status: 'verified',
          address: contractAddress,
          deployedAt: contractInfo.deployedAt,
          transactionCount: contractInfo.transactionCount || 0,
          balance: contractInfo.balance || '0'
        }
        
        console.log(`✅ ${contractName} verified successfully`)
        return true
      } else {
        throw new Error(`Contract not found: ${response.data.message}`)
      }
    } catch (error) {
      console.error(`❌ ${contractName} verification failed:`, error.message)
      
      this.verificationResults.contracts[contractName] = {
        status: 'failed',
        address: contractAddress,
        error: error.message
      }
      
      return false
    }
  }

  /**
   * Test contract functionality
   */
  async testContractFunctionality(deploymentRecord) {
    console.log('🧪 Testing contract functionality...')
    
    const client = this.createApiClient()
    
    // Test Energy Token
    if (deploymentRecord.contracts.energyToken) {
      try {
        const tokenAddress = deploymentRecord.contracts.energyToken.address
        
        // Get token info
        const tokenResponse = await client.get(`/api/v1/token/${tokenAddress}`)
        
        if (tokenResponse.data.success) {
          const tokenInfo = tokenResponse.data.data
          
          this.verificationResults.contracts.energyToken.totalSupply = tokenInfo.totalSupply
          this.verificationResults.contracts.energyToken.symbol = tokenInfo.symbol
          this.verificationResults.contracts.energyToken.decimals = tokenInfo.decimals
          
          console.log(`✅ Energy Token functional - Supply: ${tokenInfo.totalSupply} ${tokenInfo.symbol}`)
        }
      } catch (error) {
        console.error('❌ Energy Token functionality test failed:', error.message)
      }
    }

    // Test Marketplace
    if (deploymentRecord.contracts.marketplace) {
      try {
        const marketplaceAddress = deploymentRecord.contracts.marketplace.address
        
        // Test marketplace configuration
        const configResponse = await client.post('/api/v1/contract/call', {
          contractAddress: marketplaceAddress,
          method: 'getConfiguration',
          parameters: []
        })
        
        if (configResponse.data.success) {
          const config = configResponse.data.data
          
          this.verificationResults.contracts.marketplace.configuration = config
          console.log('✅ Marketplace configuration verified')
        }
      } catch (error) {
        console.error('❌ Marketplace functionality test failed:', error.message)
      }
    }

    // Test Oracle
    if (deploymentRecord.contracts.oracle) {
      try {
        const oracleAddress = deploymentRecord.contracts.oracle.address
        
        // Test oracle status
        const oracleResponse = await client.post('/api/v1/contract/call', {
          contractAddress: oracleAddress,
          method: 'getStatus',
          parameters: []
        })
        
        if (oracleResponse.data.success) {
          const status = oracleResponse.data.data
          
          this.verificationResults.contracts.oracle.status = status
          console.log('✅ Oracle status verified')
        }
      } catch (error) {
        console.error('❌ Oracle functionality test failed:', error.message)
      }
    }
  }

  /**
   * Test API integration
   */
  async testApiIntegration() {
    console.log('🔌 Testing API integration...')
    
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3001'
    
    try {
      // Test health endpoint
      const healthResponse = await axios.get(`${backendUrl}/health`)
      
      if (healthResponse.status === 200) {
        this.verificationResults.integrations.backend = {
          status: 'healthy',
          version: healthResponse.data.version,
          timestamp: healthResponse.data.timestamp
        }
        console.log('✅ Backend API healthy')
      }
    } catch (error) {
      console.error('❌ Backend API test failed:', error.message)
      this.verificationResults.integrations.backend = {
        status: 'failed',
        error: error.message
      }
    }

    try {
      // Test MasChain API connectivity
      const client = this.createApiClient()
      const apiResponse = await client.get('/api/v1/health')
      
      if (apiResponse.data.success) {
        this.verificationResults.integrations.maschain = {
          status: 'connected',
          environment: this.environment,
          timestamp: new Date().toISOString()
        }
        console.log('✅ MasChain API connected')
      }
    } catch (error) {
      console.error('❌ MasChain API test failed:', error.message)
      this.verificationResults.integrations.maschain = {
        status: 'failed',
        error: error.message
      }
    }
  }

  /**
   * Test database connectivity
   */
  async testDatabaseConnectivity() {
    console.log('🗄️ Testing database connectivity...')
    
    try {
      const { Pool } = require('pg')
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
      })
      
      const result = await pool.query('SELECT NOW() as timestamp, version() as version')
      
      if (result.rows.length > 0) {
        this.verificationResults.integrations.database = {
          status: 'connected',
          version: result.rows[0].version,
          timestamp: result.rows[0].timestamp
        }
        console.log('✅ Database connected')
      }
      
      await pool.end()
    } catch (error) {
      console.error('❌ Database test failed:', error.message)
      this.verificationResults.integrations.database = {
        status: 'failed',
        error: error.message
      }
    }
  }

  /**
   * Test Redis connectivity
   */
  async testRedisConnectivity() {
    console.log('📦 Testing Redis connectivity...')
    
    try {
      const Redis = require('ioredis')
      const redis = new Redis(process.env.REDIS_URL)
      
      await redis.ping()
      const info = await redis.info('server')
      
      this.verificationResults.integrations.redis = {
        status: 'connected',
        info: info.split('\n')[1], // Redis version line
        timestamp: new Date().toISOString()
      }
      
      console.log('✅ Redis connected')
      await redis.quit()
    } catch (error) {
      console.error('❌ Redis test failed:', error.message)
      this.verificationResults.integrations.redis = {
        status: 'failed',
        error: error.message
      }
    }
  }

  /**
   * Performance benchmarks
   */
  async runPerformanceBenchmarks() {
    console.log('⚡ Running performance benchmarks...')
    
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3001'
    
    try {
      // Test API response times
      const endpoints = [
        '/health',
        '/api/market/stats',
        '/api/energy/readings'
      ]
      
      for (const endpoint of endpoints) {
        const startTime = Date.now()
        
        try {
          await axios.get(`${backendUrl}${endpoint}`)
          const responseTime = Date.now() - startTime
          
          this.verificationResults.performance[endpoint] = {
            responseTime,
            status: responseTime < 1000 ? 'good' : responseTime < 3000 ? 'acceptable' : 'slow'
          }
          
          console.log(`📊 ${endpoint}: ${responseTime}ms`)
        } catch (error) {
          this.verificationResults.performance[endpoint] = {
            responseTime: Date.now() - startTime,
            status: 'failed',
            error: error.message
          }
        }
      }
    } catch (error) {
      console.error('❌ Performance benchmark failed:', error.message)
    }
  }

  /**
   * Generate verification report
   */
  generateReport() {
    console.log('\n📋 Generating verification report...')
    
    // Calculate overall status
    const contractsHealthy = Object.values(this.verificationResults.contracts)
      .every(contract => contract.status === 'verified')
    
    const integrationsHealthy = Object.values(this.verificationResults.integrations)
      .every(integration => integration.status === 'connected' || integration.status === 'healthy')
    
    this.verificationResults.overall = contractsHealthy && integrationsHealthy ? 'passed' : 'failed'
    
    // Save report
    const reportDir = path.join(__dirname, '..', 'deployments', 'verification')
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true })
    }
    
    const reportFile = path.join(reportDir, `verification-${this.environment}-${Date.now()}.json`)
    fs.writeFileSync(reportFile, JSON.stringify(this.verificationResults, null, 2))
    
    console.log(`📄 Verification report saved: ${reportFile}`)
    
    // Print summary
    console.log('\n🎯 Verification Summary:')
    console.log(`Overall Status: ${this.verificationResults.overall.toUpperCase()}`)
    console.log(`Environment: ${this.environment}`)
    console.log(`Contracts: ${Object.keys(this.verificationResults.contracts).length}`)
    console.log(`Integrations: ${Object.keys(this.verificationResults.integrations).length}`)
    
    if (this.verificationResults.overall === 'passed') {
      console.log('\n🎉 All verifications passed! Deployment is ready for production.')
    } else {
      console.log('\n⚠️ Some verifications failed. Please review the report and fix issues.')
    }
  }

  /**
   * Main verification process
   */
  async verify() {
    try {
      console.log(`🔍 Starting deployment verification for ${this.environment}...`)
      
      // Load deployment record
      const deploymentRecord = this.loadDeploymentRecord()
      console.log(`📄 Loaded deployment record from ${deploymentRecord.timestamp}`)
      
      // Verify contracts
      for (const [contractName, contractInfo] of Object.entries(deploymentRecord.contracts)) {
        await this.verifyContract(contractName, contractInfo.address)
      }
      
      // Test contract functionality
      await this.testContractFunctionality(deploymentRecord)
      
      // Test integrations
      await this.testApiIntegration()
      await this.testDatabaseConnectivity()
      await this.testRedisConnectivity()
      
      // Run performance benchmarks
      await this.runPerformanceBenchmarks()
      
      // Generate report
      this.generateReport()
      
    } catch (error) {
      console.error('💥 Verification failed:', error.message)
      
      this.verificationResults.overall = 'failed'
      this.verificationResults.error = error.message
      this.generateReport()
      
      process.exit(1)
    }
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new DeploymentVerifier()
  verifier.verify()
}

module.exports = DeploymentVerifier
