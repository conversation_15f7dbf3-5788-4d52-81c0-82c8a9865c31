[workspace]
members = [
    "energy-credit",
    "energy-market", 
    "energy-oracle"
]

[workspace.dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
borsh = { version = "0.10", features = ["derive"] }
thiserror = "1.0"
solana-program = "~1.16.0"
anchor-lang = "0.28.0"
anchor-spl = "0.28.0"

[profile.release]
overflow-checks = true
lto = "fat"
codegen-units = 1

[profile.release.build-override]
opt-level = 3
incremental = false
codegen-units = 1
