-- Performance optimization indexes for MasChain Energy Trading Platform
-- Run this script to create indexes for better query performance

-- Energy Offers table indexes
CREATE INDEX IF NOT EXISTS idx_energy_offers_seller ON energy_offers(seller);
CREATE INDEX IF NOT EXISTS idx_energy_offers_status ON energy_offers(status);
CREATE INDEX IF NOT EXISTS idx_energy_offers_created_at ON energy_offers(created_at);
CREATE INDEX IF NOT EXISTS idx_energy_offers_expires_at ON energy_offers(expires_at);
CREATE INDEX IF NOT EXISTS idx_energy_offers_offer_type ON energy_offers(offer_type);
CREATE INDEX IF NOT EXISTS idx_energy_offers_price_per_kwh ON energy_offers(price_per_kwh);
CREATE INDEX IF NOT EXISTS idx_energy_offers_energy_amount ON energy_offers(energy_amount);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_energy_offers_status_expires ON energy_offers(status, expires_at);
CREATE INDEX IF NOT EXISTS idx_energy_offers_seller_status ON energy_offers(seller, status);
CREATE INDEX IF NOT EXISTS idx_energy_offers_price_amount ON energy_offers(price_per_kwh, energy_amount);

-- Trades table indexes
CREATE INDEX IF NOT EXISTS idx_trades_buyer ON trades(buyer);
CREATE INDEX IF NOT EXISTS idx_trades_seller ON trades(seller);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_trades_executed_at ON trades(executed_at);
CREATE INDEX IF NOT EXISTS idx_trades_offer_id ON trades(offer_id);
CREATE INDEX IF NOT EXISTS idx_trades_blockchain_tx_id ON trades(blockchain_tx_id);

-- Composite indexes for trades
CREATE INDEX IF NOT EXISTS idx_trades_buyer_status ON trades(buyer, status);
CREATE INDEX IF NOT EXISTS idx_trades_seller_status ON trades(seller, status);
CREATE INDEX IF NOT EXISTS idx_trades_executed_at_status ON trades(executed_at, status);

-- Energy Readings table indexes
CREATE INDEX IF NOT EXISTS idx_energy_readings_wallet_address ON energy_readings(wallet_address);
CREATE INDEX IF NOT EXISTS idx_energy_readings_timestamp ON energy_readings(timestamp);
CREATE INDEX IF NOT EXISTS idx_energy_readings_type ON energy_readings(type);
CREATE INDEX IF NOT EXISTS idx_energy_readings_meter_id ON energy_readings(meter_id);

-- Composite indexes for energy readings
CREATE INDEX IF NOT EXISTS idx_energy_readings_wallet_timestamp ON energy_readings(wallet_address, timestamp);
CREATE INDEX IF NOT EXISTS idx_energy_readings_wallet_type ON energy_readings(wallet_address, type);
CREATE INDEX IF NOT EXISTS idx_energy_readings_meter_timestamp ON energy_readings(meter_id, timestamp);

-- Transaction Records table indexes
CREATE INDEX IF NOT EXISTS idx_transaction_records_from_address ON transaction_records(from_address);
CREATE INDEX IF NOT EXISTS idx_transaction_records_to_address ON transaction_records(to_address);
CREATE INDEX IF NOT EXISTS idx_transaction_records_timestamp ON transaction_records(timestamp);
CREATE INDEX IF NOT EXISTS idx_transaction_records_status ON transaction_records(status);
CREATE INDEX IF NOT EXISTS idx_transaction_records_type ON transaction_records(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transaction_records_hash ON transaction_records(transaction_hash);

-- User Activities table indexes
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_timestamp ON user_activities(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);

-- Composite indexes for user activities
CREATE INDEX IF NOT EXISTS idx_user_activities_user_timestamp ON user_activities(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_user_activities_user_type ON user_activities(user_id, activity_type);

-- Trade History table indexes
CREATE INDEX IF NOT EXISTS idx_trade_history_buyer ON trade_history(buyer);
CREATE INDEX IF NOT EXISTS idx_trade_history_seller ON trade_history(seller);
CREATE INDEX IF NOT EXISTS idx_trade_history_executed_at ON trade_history(executed_at);
CREATE INDEX IF NOT EXISTS idx_trade_history_status ON trade_history(status);

-- Audit Trail table indexes
CREATE INDEX IF NOT EXISTS idx_audit_trail_entity_type ON audit_trail(entity_type);
CREATE INDEX IF NOT EXISTS idx_audit_trail_entity_id ON audit_trail(entity_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_performed_by ON audit_trail(performed_by);
CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail(timestamp);

-- Composite indexes for audit trail
CREATE INDEX IF NOT EXISTS idx_audit_trail_entity ON audit_trail(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_entity_timestamp ON audit_trail(entity_type, entity_id, timestamp);

-- User Sessions table indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_wallet_address ON user_sessions(wallet_address);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_created_at ON user_sessions(created_at);

-- Performance Metrics table indexes
CREATE INDEX IF NOT EXISTS idx_performance_metrics_user_id ON performance_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_period ON performance_metrics(period);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);

-- Composite indexes for performance metrics
CREATE INDEX IF NOT EXISTS idx_performance_metrics_user_period ON performance_metrics(user_id, period);

-- Partial indexes for active records only (more efficient)
CREATE INDEX IF NOT EXISTS idx_energy_offers_active ON energy_offers(created_at, price_per_kwh) 
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_trades_completed ON trades(executed_at, amount) 
WHERE status = 'completed';

CREATE INDEX IF NOT EXISTS idx_energy_readings_recent ON energy_readings(timestamp, value) 
WHERE timestamp > NOW() - INTERVAL '30 days';

-- Functional indexes for common calculations
CREATE INDEX IF NOT EXISTS idx_energy_offers_remaining_amount ON energy_offers((energy_amount - COALESCE(filled_amount, 0)));

-- GIN indexes for JSON columns (if using JSONB)
-- CREATE INDEX IF NOT EXISTS idx_transaction_records_metadata ON transaction_records USING GIN(metadata);
-- CREATE INDEX IF NOT EXISTS idx_user_activities_metadata ON user_activities USING GIN(metadata);

-- Statistics update for better query planning
ANALYZE energy_offers;
ANALYZE trades;
ANALYZE energy_readings;
ANALYZE transaction_records;
ANALYZE user_activities;
ANALYZE trade_history;
ANALYZE audit_trail;
ANALYZE user_sessions;
ANALYZE performance_metrics;

-- Create materialized views for common aggregations
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_daily_market_stats AS
SELECT 
    DATE(executed_at) as trade_date,
    COUNT(*) as total_trades,
    SUM(amount) as total_volume,
    AVG(price_per_kwh) as avg_price,
    MIN(price_per_kwh) as min_price,
    MAX(price_per_kwh) as max_price,
    SUM(total_price) as total_value
FROM trades 
WHERE status = 'completed' 
    AND executed_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(executed_at)
ORDER BY trade_date DESC;

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_daily_market_stats_date ON mv_daily_market_stats(trade_date);

-- Materialized view for user performance metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_user_trading_stats AS
SELECT 
    COALESCE(t.buyer, t.seller) as user_wallet,
    COUNT(*) as total_trades,
    SUM(CASE WHEN t.seller = COALESCE(t.buyer, t.seller) THEN t.amount ELSE 0 END) as total_sold,
    SUM(CASE WHEN t.buyer = COALESCE(t.buyer, t.seller) THEN t.amount ELSE 0 END) as total_bought,
    SUM(CASE WHEN t.seller = COALESCE(t.buyer, t.seller) THEN t.total_price ELSE 0 END) as total_revenue,
    SUM(CASE WHEN t.buyer = COALESCE(t.buyer, t.seller) THEN t.total_price ELSE 0 END) as total_spent,
    AVG(t.price_per_kwh) as avg_price,
    MAX(t.executed_at) as last_trade_date
FROM trades t
WHERE t.status = 'completed'
    AND t.executed_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY COALESCE(t.buyer, t.seller);

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_user_trading_stats_wallet ON mv_user_trading_stats(user_wallet);

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_daily_market_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_trading_stats;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to refresh materialized views (requires pg_cron extension)
-- SELECT cron.schedule('refresh-mv', '0 */6 * * *', 'SELECT refresh_materialized_views();');

COMMIT;
