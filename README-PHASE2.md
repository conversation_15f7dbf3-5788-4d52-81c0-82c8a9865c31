# ⚡ MasChain Energy Trading Platform - Phase 2

## 🚀 Phase 2 - Advanced Features Release

A comprehensive blockchain-based peer-to-peer energy trading platform built on MasChain L1, now featuring real-time order matching, IoT integration, and AI-powered analytics.

### 🔥 What's New in Phase 2

- **🔄 Real-time Order Book & Matching Engine**: Live order matching with instant execution
- **📱 IoT Device Integration**: MQTT-based real-time energy meter data collection  
- **🧠 Advanced Analytics & AI**: Machine learning insights and energy forecasting
- **📊 Transaction History & Audit Trail**: Comprehensive tracking and compliance
- **🌱 Carbon Footprint Analytics**: Detailed environmental impact tracking
- **📈 Performance Metrics**: Compare your performance with network averages

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   MasChain L1   │
│   (React/Next)  │◄──►│  (Node.js/API)  │◄──►│   Blockchain    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WebSocket     │    │   Database      │    │  Smart Contracts│
│   Real-time     │    │ (PostgreSQL)    │    │  (Energy Trade) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  IoT Integration│
                       │   (MQTT/Python) │
                       └─────────────────┘
```

## 🌟 Core Features

### 📊 Real-time Trading Engine
- **Live Order Book**: Real-time bid/ask matching with market depth visualization
- **Instant Settlement**: Sub-3-second blockchain settlement via MasChain PoA
- **Market Orders**: Immediate execution at best available prices
- **Advanced Order Types**: Limit orders, market orders with slippage protection
- **WebSocket Updates**: Real-time market data streaming

### 🏠 IoT Device Management
- **Smart Meter Integration**: Real-time energy production and consumption tracking
- **MQTT Protocol Support**: Secure, reliable device communication
- **Device Verification**: Cryptographic validation of energy readings
- **Multi-device Support**: Solar panels, batteries, EV chargers, smart meters
- **Anomaly Detection**: AI-powered detection of unusual energy patterns

### 🧠 Advanced Analytics
- **AI-Powered Forecasting**: Energy production and consumption predictions
- **Market Insights**: Automated detection of trading opportunities
- **Performance Analytics**: Efficiency metrics and optimization recommendations
- **Carbon Impact Tracking**: CO₂ savings and environmental equivalents
- **Custom Alerts**: Personalized notifications for market conditions

### 🔗 Blockchain Integration
- **MasChain L1**: Malaysia's high-performance Proof of Authority blockchain
- **Smart Contract Automation**: Automated trade execution and settlement
- **Energy Credit Tokens (EC)**: Blockchain-based energy representation
- **Oracle Network**: Validated real-world energy data on-chain
- **Immutable Records**: Tamper-proof transaction and energy data history

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **UI Library**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand for React state
- **Real-time**: WebSocket connections for live updates
- **Charts**: Recharts for data visualization

### Backend
- **Runtime**: Node.js with Express.js
- **Database**: PostgreSQL with Redis caching
- **Real-time**: WebSocket server for live updates
- **IoT**: MQTT broker for device communication
- **Validation**: Express-validator for input validation

### Blockchain
- **Network**: MasChain L1 (Proof of Authority)
- **Smart Contracts**: Energy trading and token contracts
- **Wallet**: MasChain wallet integration
- **Oracle**: Real-world data validation

### IoT & Analytics
- **Protocol**: MQTT for device communication
- **Analytics**: Custom ML models for forecasting
- **Monitoring**: Real-time device health monitoring
- **Validation**: Cryptographic signature verification

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ (LTS recommended)
- Python 3.9+ with pip
- PostgreSQL 14+
- Redis 6+
- MasChain Enterprise Portal account

### 1. Clone Repository
```bash
git clone https://github.com/ifwan87/mchain-energy.git
cd mchain-energy
```

### 2. Environment Setup
```bash
cp .env.example .env
# Edit .env with your MasChain API credentials
```

### 3. Install Dependencies
```bash
# Frontend and backend dependencies
npm install

# Backend dependencies
cd backend && npm install && cd ..

# IoT integration dependencies
cd iot-integration && pip install -r requirements.txt && cd ..
```

### 4. Database Setup
```bash
# Using Docker (recommended)
docker-compose up -d postgres redis mosquitto

# Or install locally: PostgreSQL, Redis, Mosquitto MQTT
```

### 5. Start Development Environment
```bash
# Terminal 1: Backend API
cd backend && npm run dev

# Terminal 2: Frontend
npm run dev

# Terminal 3: IoT Simulator (optional)
cd iot-integration && python simulator.py
```

### 6. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api/docs

## 📱 Phase 2 Features Guide

### 🔄 Real-time Trading
1. Navigate to `/trading`
2. View live order book with real-time updates
3. Place buy/sell orders or market orders
4. Monitor trade execution and settlement

### 📱 IoT Device Management
1. Navigate to `/iot`
2. Register your energy devices
3. Monitor real-time readings
4. View device health and alerts

### 📊 Advanced Analytics
1. Navigate to `/analytics`
2. View performance metrics and benchmarks
3. Check carbon footprint analysis
4. Review AI-powered forecasts and insights

## 🧪 Testing

### Backend API Testing
```bash
cd backend
node test-api.js
```

### Frontend Testing
```bash
npm run test
```

### IoT Integration Testing
```bash
cd iot-integration
python test_mqtt.py
```

## 📊 API Endpoints

### Phase 2 New Endpoints

#### Order Book & Trading
- `GET /api/market/orderbook` - Get current order book
- `GET /api/market/depth` - Get market depth data
- `POST /api/market/orders` - Place new order
- `DELETE /api/market/orders/:id` - Cancel order
- `POST /api/market/market-order` - Execute market order

#### IoT Device Management
- `POST /api/iot/devices` - Register new device
- `GET /api/iot/devices` - Get user devices
- `POST /api/iot/readings` - Submit device reading
- `GET /api/iot/readings/:deviceId` - Get device readings
- `GET /api/iot/dashboard` - Get IoT dashboard data

#### Analytics & Insights
- `GET /api/analytics/insights` - Get market insights
- `GET /api/analytics/user-insights` - Get personalized insights
- `GET /api/analytics/forecast` - Generate forecasts
- `GET /api/analytics/carbon-footprint` - Get carbon analysis
- `GET /api/analytics/performance` - Get performance metrics

## 🔧 Configuration

### Environment Variables
```bash
# MasChain L1 Configuration
MASCHAIN_ENVIRONMENT=testnet
MASCHAIN_API_KEY=your_api_key_here
MASCHAIN_API_SECRET=your_api_secret_here
MASCHAIN_PROJECT_ID=your_project_id_here

# Application Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/maschain_energy
REDIS_URL=redis://localhost:6379

# MQTT Configuration
MQTT_BROKER_URL=mqtt://localhost:1883
MQTT_USERNAME=energy_platform
MQTT_PASSWORD=secure_password
```

## 🚀 Deployment

### Production Deployment
```bash
# Build frontend
npm run build

# Build backend
cd backend && npm run build

# Start production servers
npm run start:prod
```

### Docker Deployment
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 Performance Metrics

### Phase 2 Improvements
- **Order Matching**: < 100ms average execution time
- **WebSocket Latency**: < 50ms for real-time updates
- **IoT Data Processing**: 1000+ readings/second capacity
- **Analytics Generation**: Real-time insights and forecasts
- **Blockchain Settlement**: < 3 seconds via MasChain PoA

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.maschain.com](https://docs.maschain.com)
- **Enterprise Portal**: [portal.maschain.com](https://portal.maschain.com)
- **Issues**: [GitHub Issues](https://github.com/ifwan87/mchain-energy/issues)
- **Email**: <EMAIL>

## 🎯 Roadmap

### Phase 3 (Coming Soon)
- **Mobile App**: Native iOS/Android applications
- **Advanced ML**: Enhanced forecasting models
- **Grid Integration**: Direct utility company integration
- **Multi-chain**: Cross-chain energy trading
- **Governance**: DAO-based platform governance

---

**Built with ❤️ for the future of sustainable energy trading**
