# MasChain Energy Trading Platform - Production Deployment Guide

This guide provides step-by-step instructions for deploying the MasChain Energy Trading Platform to production.

## 📋 Prerequisites

### 1. MasChain Enterprise Portal Setup
- [ ] Register at [MasChain Enterprise Portal](https://portal.maschain.com)
- [ ] Complete KYC/KYB verification process
- [ ] Subscribe to required services:
  - [ ] Token Management Service
  - [ ] Wallet Management Service
  - [ ] Smart Contract Creation Service
- [ ] Generate production API credentials
- [ ] Fund your MasChain wallet for deployment costs

### 2. Infrastructure Requirements
- [ ] PostgreSQL 14+ database server
- [ ] Redis 6+ cache server
- [ ] Node.js 18+ runtime environment
- [ ] SSL certificates for HTTPS
- [ ] Domain name and DNS configuration
- [ ] Load balancer (recommended for high availability)

### 3. Security Requirements
- [ ] Secure environment for storing secrets
- [ ] VPN or private network access
- [ ] Backup and disaster recovery plan
- [ ] Monitoring and alerting system

## 🚀 Deployment Steps

### Step 1: Environment Setup

1. **Clone the repository:**
```bash
git clone https://github.com/ifwan87/mchain-energy.git
cd mchain-energy
```

2. **Install dependencies:**
```bash
npm install
cd backend && npm install && cd ..
cd iot-integration && pip install -r requirements.txt && cd ..
```

3. **Configure environment variables:**
```bash
cp .env.production.template .env.production
# Edit .env.production with your actual values
```

### Step 2: Database Setup

1. **Create production database:**
```sql
CREATE DATABASE maschain_energy_prod;
CREATE USER maschain_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE maschain_energy_prod TO maschain_user;
```

2. **Run database migrations:**
```bash
npm run db:migrate:production
```

3. **Create performance indexes:**
```bash
psql -d maschain_energy_prod -f database/migrations/001_create_indexes.sql
```

### Step 3: MasChain Smart Contract Deployment

1. **Deploy to testnet first (recommended):**
```bash
npm run maschain:deploy:testnet
```

2. **Verify testnet deployment:**
```bash
npm run maschain:verify:testnet
```

3. **Deploy to mainnet:**
```bash
npm run maschain:deploy:mainnet
```

4. **Verify mainnet deployment:**
```bash
npm run maschain:verify:mainnet
```

### Step 4: Application Deployment

1. **Build the application:**
```bash
npm run build
npm run backend:build
```

2. **Start production services:**
```bash
# Using PM2 (recommended)
npm install -g pm2
pm2 start ecosystem.config.js --env production

# Or using Docker
docker-compose -f docker-compose.prod.yml up -d
```

3. **Verify deployment:**
```bash
curl https://your-domain.com/health
curl https://your-domain.com/api/performance
```

## 🔧 Configuration Details

### Environment Variables

Key production environment variables to configure:

```bash
# Application
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-domain.com

# MasChain
MASCHAIN_ENVIRONMENT=mainnet
MASCHAIN_API_KEY=your_production_api_key
MASCHAIN_API_SECRET=your_production_api_secret
MASCHAIN_PROJECT_ID=your_project_id

# Database
DATABASE_URL=********************************/maschain_energy_prod
DB_POOL_MAX=50

# Redis
REDIS_URL=redis://user:pass@host:6379

# Security
JWT_SECRET=your_very_long_and_secure_jwt_secret
RATE_LIMIT_MAX_REQUESTS=100
```

### SSL/TLS Configuration

1. **Obtain SSL certificates:**
```bash
# Using Let's Encrypt
certbot certonly --webroot -w /var/www/html -d your-domain.com
```

2. **Configure NGINX (example):**
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 Monitoring and Maintenance

### Health Checks

Set up monitoring for these endpoints:
- `GET /health` - Application health
- `GET /api/performance` - Performance metrics
- Database connectivity
- Redis connectivity
- MasChain API connectivity

### Performance Monitoring

1. **Enable performance monitoring:**
```bash
ENABLE_PERFORMANCE_MONITORING=true
```

2. **Monitor key metrics:**
- API response times
- Database query performance
- Memory and CPU usage
- Error rates
- Transaction throughput

### Backup Strategy

1. **Database backups:**
```bash
# Daily automated backup
0 2 * * * pg_dump maschain_energy_prod | gzip > /backups/db_$(date +%Y%m%d).sql.gz
```

2. **Configuration backups:**
- Environment variables
- SSL certificates
- Deployment records

### Log Management

1. **Configure log rotation:**
```bash
# /etc/logrotate.d/maschain-energy
/var/log/maschain-energy/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
}
```

## 🔒 Security Checklist

- [ ] All secrets stored securely (not in code)
- [ ] Database connections encrypted
- [ ] API rate limiting enabled
- [ ] CORS properly configured
- [ ] SSL/TLS certificates valid
- [ ] Regular security updates applied
- [ ] Access logs monitored
- [ ] Intrusion detection system active

## 🚨 Troubleshooting

### Common Issues

1. **MasChain API Connection Failed**
```bash
# Check API credentials
curl -H "Authorization: Bearer $MASCHAIN_API_KEY" \
     -H "X-API-Secret: $MASCHAIN_API_SECRET" \
     https://service.maschain.com/api/v1/health
```

2. **Database Connection Issues**
```bash
# Test database connection
psql $DATABASE_URL -c "SELECT version();"
```

3. **Redis Connection Issues**
```bash
# Test Redis connection
redis-cli -u $REDIS_URL ping
```

### Performance Issues

1. **Slow API responses:**
- Check database indexes
- Monitor Redis cache hit rates
- Review slow query logs

2. **High memory usage:**
- Check for memory leaks
- Optimize database queries
- Tune garbage collection

### Recovery Procedures

1. **Database recovery:**
```bash
# Restore from backup
gunzip -c /backups/db_20240115.sql.gz | psql maschain_energy_prod
```

2. **Application recovery:**
```bash
# Restart services
pm2 restart all
# Or with Docker
docker-compose restart
```

## 📞 Support and Maintenance

### Regular Maintenance Tasks

- [ ] Weekly security updates
- [ ] Monthly performance reviews
- [ ] Quarterly disaster recovery tests
- [ ] Annual security audits

### Support Contacts

- **Technical Issues**: <EMAIL>
- **MasChain Support**: https://portal.maschain.com/support
- **Emergency Hotline**: +60-XXX-XXXX

### Documentation Updates

Keep this deployment guide updated with:
- New configuration options
- Security patches
- Performance optimizations
- Lessons learned from incidents

---

**Last Updated**: January 2025  
**Version**: 1.0.0  
**Maintainer**: MasChain Energy Trading Platform Team
