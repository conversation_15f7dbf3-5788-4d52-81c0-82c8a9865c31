# MasChain Energy Trading Platform - Architecture Overview

## 📋 Executive Summary

The MasChain Energy Trading Platform is a comprehensive blockchain-based peer-to-peer energy trading system that enables homeowners with renewable energy sources to trade excess energy directly with their neighbors using Malaysia's Layer 1 Proof of Authority blockchain technology.

## 🏗️ System Architecture

### High-Level Architecture

The platform follows a microservices architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   MasChain L1   │
│   (React/Next)  │◄──►│  (Node.js/API)  │◄──►│   Blockchain    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Wallet   │    │   Database      │    │  Smart Contracts│
│   (MasChain)    │    │ (PostgreSQL)    │    │  (Energy Trade) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  IoT Integration│
                       │   (Python/MQTT) │
                       └─────────────────┘
```

### Core Components

#### 1. Frontend Layer (React/Next.js)
- **Technology**: Next.js 14 with TypeScript, Tailwind CSS
- **State Management**: Zustand for global state
- **Key Features**:
  - Trading Dashboard with real-time order book
  - Analytics Dashboard with energy insights
  - IoT Device Management
  - Wallet Integration (MasChain)
  - Responsive design for mobile/desktop

#### 2. Backend Layer (Node.js/Express)
- **Technology**: Node.js with Express.js, TypeScript
- **Architecture**: RESTful API with WebSocket support
- **Services**:
  - Authentication & Authorization
  - Energy Data Management
  - Market & Trading Logic
  - Oracle Data Validation
  - IoT Device Communication
  - Real-time Analytics

#### 3. Blockchain Layer (MasChain L1)
- **Network**: MasChain Layer 1 (Proof of Authority)
- **Smart Contracts**:
  - Energy Credit Token (ERC-20 compatible)
  - Energy Marketplace (Trading logic)
  - Energy Oracle (Data validation)
- **Features**:
  - Sub-3 second transaction finality
  - Low transaction fees
  - Enterprise-grade security

#### 4. Data Layer
- **Primary Database**: PostgreSQL for transactional data
- **Cache Layer**: Redis for session management and real-time data
- **Message Broker**: MQTT for IoT device communication
- **File Storage**: Local/cloud storage for logs and reports

#### 5. IoT Integration Layer
- **Protocol**: MQTT for real-time communication
- **Client**: Python-based meter client
- **Supported Devices**:
  - Smart energy meters
  - Solar panel inverters
  - Wind turbine controllers
  - Battery storage systems

## 🔄 Data Flow Architecture

### Energy Production Flow
1. **IoT Devices** generate energy readings
2. **MQTT Client** publishes data to broker
3. **Backend IoT Service** processes and validates readings
4. **Oracle Service** submits verified data to blockchain
5. **Smart Contract** mints energy credits
6. **Frontend** displays real-time updates via WebSocket

### Trading Flow
1. **User** creates energy offer through frontend
2. **Backend** validates and stores offer
3. **Smart Contract** creates on-chain offer
4. **Order Book** matches buyers and sellers
5. **Smart Contract** executes trade automatically
6. **Blockchain** settles energy credit transfer
7. **Frontend** confirms trade completion

## 🛠️ Technology Stack

### Frontend Technologies
```json
{
  "framework": "Next.js 14.2.29",
  "language": "TypeScript 5.2.2",
  "styling": "Tailwind CSS 3.3.5",
  "state": "Zustand 4.4.6",
  "ui": "Custom components + Lucide React",
  "charts": "Recharts 2.8.0",
  "notifications": "React Hot Toast 2.4.1"
}
```

### Backend Technologies
```json
{
  "runtime": "Node.js 18+",
  "framework": "Express.js 4.18.2",
  "language": "TypeScript",
  "database": "PostgreSQL 15",
  "cache": "Redis 7",
  "websocket": "ws 8.14.2",
  "validation": "Express Validator 7.0.1",
  "security": "Helmet 7.1.0, CORS 2.8.5"
}
```

### Blockchain Technologies
```json
{
  "blockchain": "MasChain L1",
  "consensus": "Proof of Authority",
  "smart_contracts": "Rust-based",
  "wallet": "MasChain Wallet System",
  "api": "MasChain Enterprise Portal"
}
```

### IoT Technologies
```json
{
  "language": "Python 3.9+",
  "protocol": "MQTT",
  "broker": "Eclipse Mosquitto 2.0",
  "encryption": "TLS/SSL",
  "authentication": "Certificate-based"
}
```

## 📁 Project Structure

```
mchain-energy/
├── src/                          # Frontend source code
│   ├── app/                      # Next.js app directory
│   │   ├── trading/              # Trading page
│   │   ├── analytics/            # Analytics page
│   │   ├── iot/                  # IoT dashboard
│   │   └── dashboard/            # Main dashboard
│   ├── components/               # React components
│   │   ├── ui/                   # Reusable UI components
│   │   ├── trading/              # Trading-specific components
│   │   ├── analytics/            # Analytics components
│   │   ├── iot/                  # IoT components
│   │   └── providers/            # Context providers
│   ├── store/                    # Zustand state management
│   └── utils/                    # Utility functions
├── backend/                      # Backend API server
│   ├── src/
│   │   ├── routes/               # API route handlers
│   │   ├── services/             # Business logic services
│   │   ├── middleware/           # Express middleware
│   │   └── types/                # TypeScript definitions
├── smart-contracts/              # Blockchain smart contracts
│   ├── energy-credit/            # Energy token contract
│   ├── energy-market/            # Trading marketplace
│   └── energy-oracle/            # Oracle service
├── iot-integration/              # IoT services
│   └── meter-client/             # Python MQTT client
├── docs/                         # Documentation
└── scripts/                      # Deployment scripts
```

## 🔗 Integration Points

### API Endpoints
```typescript
// Energy Management
GET    /api/energy/stats          // Energy statistics
GET    /api/energy/readings       // Meter readings
POST   /api/energy/readings       // Submit reading

// Market Operations
GET    /api/market/offers         // Active offers
POST   /api/market/offers         // Create offer
POST   /api/market/trade          // Execute trade
GET    /api/market/orderbook      // Order book data

// IoT Management
GET    /api/iot/devices           // Connected devices
POST   /api/iot/devices           // Register device
GET    /api/iot/readings          // Device readings

// Analytics
GET    /api/analytics/overview    // Platform metrics
GET    /api/analytics/forecasts   // Energy forecasts
```

### WebSocket Events
```typescript
// Real-time updates
'energy_reading'     // New IoT reading
'market_update'      // Order book changes
'trade_executed'     // Trade completion
'device_status'      // IoT device status
'price_update'       // Market price changes
```

### MQTT Topics
```
energy/{device_id}/readings      // Energy meter data
energy/{device_id}/status        // Device health
energy/{device_id}/alerts        // Device alerts
energy/grid/demand              // Grid demand data
```

## 🔒 Security Architecture

### Authentication & Authorization
- JWT-based session management
- Role-based access control (RBAC)
- API rate limiting and throttling
- Input validation and sanitization

### Blockchain Security
- Cryptographic signature verification
- Multi-signature wallet support
- Smart contract audit trails
- Transaction replay protection

### IoT Security
- Certificate-based device authentication
- Encrypted MQTT communication (TLS)
- Data integrity verification
- Anomaly detection algorithms

### Data Protection
- Database encryption at rest
- API communication over HTTPS
- Sensitive data masking in logs
- GDPR compliance measures

## 📊 Performance Characteristics

### Blockchain Performance
- **Transaction Finality**: < 3 seconds
- **Throughput**: 1000+ TPS
- **Gas Fees**: Significantly lower than Ethereum
- **Uptime**: 99.9% availability

### System Performance
- **API Response Time**: < 200ms average
- **WebSocket Latency**: < 50ms
- **Database Queries**: < 100ms average
- **IoT Data Processing**: Real-time (< 1 second)

### Scalability
- **Horizontal Scaling**: Microservices architecture
- **Database Sharding**: Supported for large datasets
- **CDN Integration**: Static asset optimization
- **Load Balancing**: Multi-instance deployment

## 🚀 Deployment Architecture

### Development Environment
- Local development with Docker Compose
- Hot reloading for frontend and backend
- Mock IoT devices for testing
- Local blockchain testnet

### Production Environment
- Kubernetes orchestration
- Multi-region deployment
- Auto-scaling based on demand
- Comprehensive monitoring and alerting

### CI/CD Pipeline
- Automated testing (unit, integration, e2e)
- Security scanning and vulnerability assessment
- Automated deployment to staging/production
- Rollback capabilities for quick recovery

---

*This architecture overview provides a comprehensive understanding of the MasChain Energy Trading Platform's technical foundation and design principles.*
